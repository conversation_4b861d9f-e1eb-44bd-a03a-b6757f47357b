<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'google_id',
        'avatar',
        'role_id',
        'student_id',
        'teacher_id',
        'class',
        'grade',
        'phone',
        'address',
        'birth_date',
        'gender',
        'is_active'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'google_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'birth_date' => 'date',
        'is_active' => 'boolean'
    ];

    /**
     * Get the user's full name.
     *
     * @return string
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get the user's role.
     */
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get form responses for the user.
     */
    public function formResponses()
    {
        return $this->hasMany(FormResponse::class);
    }

    /**
     * Get students if user is a parent.
     */
    public function children()
    {
        return $this->belongsToMany(User::class, 'student_parent_relationships', 'parent_id', 'student_id')
                    ->withPivot('relationship_type', 'is_primary', 'can_view_results')
                    ->withTimestamps();
    }

    /**
     * Get parents if user is a student.
     */
    public function parents()
    {
        return $this->belongsToMany(User::class, 'student_parent_relationships', 'student_id', 'parent_id')
                    ->withPivot('relationship_type', 'is_primary', 'can_view_results')
                    ->withTimestamps();
    }

    /**
     * Get students if user is a teacher.
     */
    public function students()
    {
        return $this->hasMany(User::class, 'teacher_id', 'id');
    }

    /**
     * Get teacher if user is a student.
     */
    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    // Role checking methods
    public function isAdmin()
    {
        return $this->role && $this->role->name === Role::ADMIN;
    }

    public function isGuru()
    {
        return $this->role && $this->role->name === Role::GURU;
    }

    public function isOrangtua()
    {
        return $this->role && $this->role->name === Role::ORANGTUA;
    }

    public function isSiswa()
    {
        return $this->role && $this->role->name === Role::SISWA;
    }

    public function hasPermission($permission)
    {
        return $this->role && $this->role->hasPermission($permission);
    }

    public function canTakeAssessment()
    {
        return $this->isSiswa() && $this->is_active;
    }

    public function canViewStudentResults()
    {
        return $this->isAdmin() || $this->isGuru() || $this->isOrangtua();
    }

    public function canManageUsers()
    {
        return $this->isAdmin();
    }

    /**
     * Get all assessments for the user.
     */
    public function assessments()
    {
        return $this->hasMany(Assessment::class);
    }

    /**
     * Get the latest assessment for the user.
     */
    public function latestAssessment()
    {
        return $this->hasOne(Assessment::class)->latestOfMany();
    }

    /**
     * Get monthly assessment statistics.
     */
    public function getMonthlyStats()
    {
        return $this->assessments()
            ->selectRaw('COUNT(*) as total_assessments')
            ->selectRaw('AVG(total_score) as average_score')
            ->selectRaw('COUNT(CASE WHEN status = "concern" THEN 1 END) as concerns')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->first();
    }
}
