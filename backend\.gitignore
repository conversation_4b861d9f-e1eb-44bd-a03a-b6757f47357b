# Laravel Backend .gitignore

# Dependencies
/node_modules
/vendor

# Laravel specific
/.phpunit.cache
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*

# Environment files
.env
.env.backup
.env.production
.env.local
.env.testing

# IDE and Editor files
/.fleet
/.idea
/.vscode
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
Thumbs.db
ehthumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Laravel Homestead
Homestead.json
Homestead.yaml
auth.json

# Laravel Sail
docker-compose.override.yml

# Testing
/coverage
/.phpunit.result.cache

# Temporary files
*.tmp
*.temp

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Database
*.sqlite
*.sqlite-journal

# Laravel Mix
/public/js/app.js
/public/css/app.css
/public/mix-manifest.json

# Laravel Nova (if used)
/nova

# Telescope (if used)
/storage/telescope

# Debugbar (if used)
/storage/debugbar
