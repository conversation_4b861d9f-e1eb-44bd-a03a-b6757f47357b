# 🎨 SantriMental Modern Template Guide

## 📋 Overview

This guide documents the modern template system implemented for SantriMental platform. The new template system provides a consistent, responsive, and modern UI/UX across all dashboard interfaces.

## 🏗️ Template Structure

### Base Template: `modern-template.blade.php`

The base template provides a consistent layout structure with the following features:

- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Glass Morphism UI**: Modern translucent design elements
- **Dark/Light Theme Support**: Toggle between themes
- **Modular Components**: Reusable sections and components
- **Animation System**: Smooth transitions and effects

### Template Sections

```blade
@extends('modern-template')

@section('title', 'Page Title')
@section('logo-icon', 'icon-name')
@section('logo-gradient', 'gradient-class')
@section('dashboard-title', 'Dashboard Name')
@section('page-title', 'Page Title')
@section('page-subtitle', 'Page Description')
@section('user-initials', 'UI')
@section('user-name', 'User Name')
@section('user-email', '<EMAIL>')
@section('user-gradient', 'gradient-class')

@section('navigation')
    <!-- Navigation menu items -->
@endsection

@section('header-actions')
    <!-- Header action buttons -->
@endsection

@section('breadcrumb')
    <!-- Breadcrumb navigation -->
@endsection

@section('content')
    <!-- Main page content -->
@endsection

@push('styles')
    <!-- Additional CSS -->
@endpush

@push('scripts')
    <!-- Additional JavaScript -->
@endpush
```

## 🎯 Available Templates

### 1. Index/Landing Page
- **File**: `index-modern.blade.php`
- **Route**: `/`
- **Features**: 
  - Dashboard selection interface
  - Feature showcase
  - System status indicators
  - Modern animations

### 2. Student Dashboard
- **File**: `student-dashboard-modern.blade.php`
- **Route**: `/dashboard`
- **Features**:
  - Mental health statistics
  - Quick assessment actions
  - Progress tracking charts
  - Daily mental health tips

### 3. Parent Dashboard
- **File**: `parent-dashboard-modern.blade.php`
- **Route**: `/orangtua/dashboard`
- **Features**:
  - Children monitoring cards
  - Assessment history table
  - Family-centered design
  - Consultation tools

### 4. Admin Dashboard
- **File**: `admin-dashboard-modern.blade.php`
- **Route**: `/admin/dashboard`
- **Features**:
  - System monitoring
  - User management tools
  - Performance charts
  - Activity logs

### 5. Teacher Dashboard
- **File**: `teacher-dashboard-modern.blade.php`
- **Route**: `/guru/dashboard`
- **Features**:
  - Student monitoring
  - Class statistics
  - Alert system for at-risk students
  - Educational resources

## 🎨 Design System

### Color Palette

```css
/* Primary Colors */
--primary-500: #3b82f6;
--secondary-500: #d946ef;
--success-500: #22c55e;
--warning-500: #f59e0b;

/* Gradient Classes */
.gradient-primary: linear-gradient(135deg, #3b82f6, #2563eb);
.gradient-secondary: linear-gradient(135deg, #d946ef, #c026d3);
.gradient-success: linear-gradient(135deg, #22c55e, #16a34a);
.gradient-warning: linear-gradient(135deg, #f59e0b, #d97706);
```

### Typography

- **Primary Font**: Inter (Google Fonts)
- **Monospace Font**: JetBrains Mono
- **Font Weights**: 300, 400, 500, 600, 700, 800, 900

### Components

#### Modern Card
```html
<div class="modern-card p-6 animate-fade-in-up">
    <!-- Card content -->
</div>
```

#### Stats Card
```html
<div class="stats-card">
    <div class="flex items-center justify-between mb-4">
        <div class="p-3 gradient-primary rounded-xl shadow-lg">
            <i data-lucide="icon-name" class="w-6 h-6 text-white"></i>
        </div>
        <div class="stats-change positive">
            <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
            +12%
        </div>
    </div>
    <div>
        <p class="stats-label">Label</p>
        <p class="stats-value">Value</p>
        <p class="text-xs text-white/50 mt-1">Description</p>
    </div>
</div>
```

#### Glass Card
```html
<div class="glass-card p-4 rounded-xl">
    <!-- Glass morphism content -->
</div>
```

#### Buttons
```html
<!-- Primary Button -->
<button class="btn-primary">
    <i data-lucide="icon" class="w-4 h-4 mr-2"></i>
    Button Text
</button>

<!-- Ghost Button -->
<button class="btn-ghost">Button Text</button>

<!-- Success Button -->
<button class="btn-success">Button Text</button>

<!-- Warning Button -->
<button class="btn-warning">Button Text</button>
```

#### Badges
```html
<span class="badge badge-primary">Primary</span>
<span class="badge badge-success">Success</span>
<span class="badge badge-warning">Warning</span>
```

## 🔧 Configuration Options

### Template Variables

The base template accepts several configuration variables:

```php
// Hide navigation sidebar
['hideNavigation' => true]

// Hide header section
['hideHeader' => true]

// Custom CSS classes
['bodyClass' => 'custom-class']
```

### Icon System

The template uses Lucide icons. Common icons:

- `brain` - Main logo
- `graduation-cap` - Student
- `heart` - Parent
- `shield-check` - Admin
- `user-check` - Teacher
- `layout-dashboard` - Dashboard
- `users` - Users
- `clipboard-check` - Assessment
- `bar-chart-3` - Analytics

## 📱 Responsive Breakpoints

```css
/* Mobile */
@media (max-width: 768px) {
    /* Single column layout */
    /* Collapsible navigation */
}

/* Tablet */
@media (min-width: 768px) and (max-width: 1024px) {
    /* Two column layout */
    /* Adapted navigation */
}

/* Desktop */
@media (min-width: 1024px) {
    /* Full layout with sidebar */
    /* All features visible */
}
```

## ⚡ Performance Features

### Lazy Loading
- Progressive content loading
- Skeleton screens during load
- Optimized image loading

### Animations
- CSS-based animations
- Hardware acceleration
- Reduced motion support

### Optimization
- Minified assets
- Efficient DOM manipulation
- Optimized rendering

## 🔄 Migration from Old Templates

### Route Updates

Old routes are preserved with `-old` suffix:

```php
// New modern templates
Route::get('/dashboard', 'student-dashboard-modern');
Route::get('/admin/dashboard', 'admin-dashboard-modern');
Route::get('/guru/dashboard', 'teacher-dashboard-modern');
Route::get('/orangtua/dashboard', 'parent-dashboard-modern');

// Old templates (backup)
Route::get('/dashboard-old', 'dashboard');
Route::get('/admin/dashboard-old', 'admin-dashboard');
Route::get('/guru/dashboard-old', 'guru-dashboard');
Route::get('/orangtua/dashboard-old', 'orangtua-dashboard');
```

### Template Conversion

To convert an old template to the modern system:

1. **Extend the base template**:
   ```blade
   @extends('modern-template')
   ```

2. **Define sections**:
   ```blade
   @section('title', 'Page Title')
   @section('page-title', 'Dashboard Title')
   ```

3. **Move navigation to section**:
   ```blade
   @section('navigation')
       <!-- Navigation items -->
   @endsection
   ```

4. **Update content structure**:
   ```blade
   @section('content')
       <!-- Page content with modern components -->
   @endsection
   ```

## 🎯 Best Practices

### Component Usage
- Use consistent spacing with Tailwind classes
- Apply proper animation delays for staggered effects
- Maintain consistent color schemes per role

### Accessibility
- Include proper ARIA labels
- Ensure keyboard navigation
- Maintain color contrast ratios

### Performance
- Minimize DOM manipulation
- Use CSS transforms for animations
- Optimize chart rendering

## 🚀 Future Enhancements

### Planned Features
- [ ] Advanced theme customization
- [ ] Component library expansion
- [ ] Enhanced animation system
- [ ] PWA capabilities
- [ ] Advanced accessibility features

### Template Extensions
- [ ] Email templates
- [ ] Print layouts
- [ ] Mobile app views
- [ ] Widget system

## 📞 Support

For questions or issues with the modern template system:

1. Check this documentation
2. Review the base template code
3. Test with the old template routes
4. Contact the development team

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Compatibility**: Laravel 10+, Tailwind CSS 3+
