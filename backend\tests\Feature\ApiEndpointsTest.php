<?php

namespace Tests\Feature;

use Tests\TestCase;

class ApiEndpointsTest extends TestCase
{
    /**
     * Test that the home page loads successfully.
     */
    public function test_home_page_loads_successfully(): void
    {
        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertSee('SantriMental');
    }

    /**
     * Test that API routes are now public (authentication disabled).
     */
    public function test_api_routes_are_now_public(): void
    {
        $response = $this->getJson('/api/forms');
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }

    /**
     * Test that API fallback route returns 404.
     */
    public function test_api_fallback_returns_404(): void
    {
        $response = $this->getJson('/api/nonexistent-endpoint');
        $response->assertStatus(404);
        $response->assertJson(['message' => 'Route not found.']);
    }

    /**
     * Test that QR login endpoint exists (public endpoint).
     */
    public function test_qr_login_endpoint_exists(): void
    {
        $response = $this->postJson('/api/auth/qr', []);
        // Should return validation error or method not allowed, not 404
        $this->assertNotEquals(404, $response->getStatusCode());
    }

    /**
     * Test that admin endpoints are now public.
     */
    public function test_admin_endpoints_are_public(): void
    {
        $response = $this->getJson('/api/admin/users');
        $response->assertStatus(200);
        $response->assertJson(['message' => 'Admin users endpoint - Authentication disabled']);
    }

    /**
     * Test that dashboard stats endpoint works.
     */
    public function test_dashboard_stats_endpoint(): void
    {
        $response = $this->getJson('/api/dashboard/stats');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_assessments',
                'latest_assessment',
                'monthly_stats',
                'trend'
            ]
        ]);
    }
}
