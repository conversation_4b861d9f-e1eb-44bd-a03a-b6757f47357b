@extends('tailadmin-base')

@section('title', 'Dashboard Siswa - SantriMental')
@section('dashboard-subtitle', 'Dashboard Siswa')
@section('page-title', 'Dashboard Siswa')
@section('page-description', 'Selamat datang! Monitor kese<PERSON>an mental Anda dengan mudah dan aman.')
@section('user-initials', 'SS')
@section('user-name', 'Siswa Demo')
@section('user-role', 'Student')

@section('logo-icon')
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
</svg>
@endsection

@section('navigation')
<div class="mb-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6">Menu Utama</p>

    <a href="#" class="tailadmin-nav-item active">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="font-medium">Dashboard</span>
        <div class="ml-auto">
            <div class="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
        </div>
    </a>

    <a href="{{ route('assessments') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
        </svg>
        <span class="font-medium">Skrining Mental</span>
        <div class="ml-auto">
            <span class="tailadmin-badge primary text-xs">Baru</span>
        </div>
    </a>

    <a href="{{ route('history') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Riwayat Tes</span>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <span class="font-medium">Panduan Kesehatan</span>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <span class="font-medium">Konsultasi</span>
        <div class="ml-auto">
            <span class="tailadmin-badge warning text-xs">Soon</span>
        </div>
    </a>
</div>

<div class="border-t border-gray-200 dark:border-dark-300 pt-4 mt-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6">Akun</p>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        <span class="font-medium">Profil Saya</span>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        <span class="font-medium">Pengaturan</span>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Bantuan</span>
    </a>
</div>
@endsection

@section('header-actions')
<!-- Quick Assessment -->
<button class="tailadmin-btn tailadmin-btn-primary" onclick="window.location.href='{{ route('assessments') }}'">
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
    </svg>
    Skrining Cepat
</button>
@endsection

@section('breadcrumb')
<a href="/" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Beranda</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<span class="tailadmin-text-primary font-medium">Dashboard Siswa</span>
@endsection

@section('content')
<!-- Welcome Banner -->
<div class="tailadmin-card mb-8 tailadmin-fade-in-up">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mr-6 shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </div>
            <div>
                <h2 class="text-2xl font-bold tailadmin-text-primary mb-2">Selamat Datang di SantriMental</h2>
                <p class="tailadmin-text-secondary text-lg">Platform kesehatan mental yang aman dan terpercaya untuk siswa</p>
                <div class="flex items-center mt-3 space-x-4">
                    <div class="flex items-center text-sm text-success-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span>Data Aman</span>
                    </div>
                    <div class="flex items-center text-sm text-primary-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>24/7 Tersedia</span>
                    </div>
                    <div class="flex items-center text-sm text-secondary-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <span>Konselor Profesional</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="hidden md:block">
            <button class="tailadmin-btn tailadmin-btn-primary text-lg px-8 py-4" onclick="window.location.href='{{ route('assessments') }}'">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Mulai Skrining
            </button>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Assessments Card -->
    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.1s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon primary">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +12%
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Total Skrining</p>
            <p class="tailadmin-stats-value">15</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Sejak bergabung</p>
        </div>
    </div>

    <!-- Latest Score Card -->
    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.2s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon success">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Baik
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Skor Terakhir</p>
            <p class="tailadmin-stats-value">4</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Dari 20 poin (SRQ-20)</p>
        </div>
    </div>

    <!-- Status Card -->
    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.3s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon success">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </div>
            <div class="w-3 h-3 bg-success-500 rounded-full animate-pulse"></div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Status Mental</p>
            <p class="text-2xl font-bold text-success-500 mb-1">Normal</p>
            <p class="text-xs tailadmin-text-secondary">Kondisi stabil</p>
        </div>
    </div>

    <!-- Monthly Count Card -->
    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.4s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon warning">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10a2 2 0 002 2h4a2 2 0 002-2V11m-6 0h6"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +3
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Bulan Ini</p>
            <p class="tailadmin-stats-value">8</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Skrining selesai</p>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: 0.5s;" onclick="window.location.href='{{ route('assessments') }}'">
        <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
                </svg>
            </div>
            <svg class="w-5 h-5 tailadmin-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </div>
        <div>
            <h3 class="text-lg font-semibold tailadmin-text-primary mb-2">Mulai Skrining</h3>
            <p class="tailadmin-text-secondary text-sm">Lakukan tes kesehatan mental SRQ-20</p>
        </div>
    </div>

    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: 0.6s;" onclick="window.location.href='{{ route('history') }}'">
        <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <svg class="w-5 h-5 tailadmin-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </div>
        <div>
            <h3 class="text-lg font-semibold tailadmin-text-primary mb-2">Lihat Riwayat</h3>
            <p class="tailadmin-text-secondary text-sm">Pantau perkembangan kesehatan mental</p>
        </div>
    </div>

    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: 0.7s;">
        <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
            </div>
            <svg class="w-5 h-5 tailadmin-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </div>
        <div>
            <h3 class="text-lg font-semibold tailadmin-text-primary mb-2">Panduan Kesehatan</h3>
            <p class="tailadmin-text-secondary text-sm">Tips dan artikel kesehatan mental</p>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Show welcome notification
    setTimeout(() => {
        showToast('Selamat datang di Dashboard Siswa! Mulai skrining untuk memantau kesehatan mental Anda.', 'success');
    }, 1000);
});
</script>
@endpush
