/**
 * Enhanced Dashboard Page Logic with Modern UI and Advanced Charts
 */
document.addEventListener('DOMContentLoaded', () => {
    const auth = window.auth;
    const Utils = window.Utils;

    // Temporarily disable authentication check for testing
    // if (!auth.isAuthenticated()) {
    //     window.location.href = '/';
    //     return;
    // }

    // Modern Chart Configuration
    Chart.defaults.font.family = 'Inter, sans-serif';
    Chart.defaults.color = 'rgba(255, 255, 255, 0.7)';
    Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
    Chart.defaults.backgroundColor = 'rgba(255, 255, 255, 0.05)';

    // Chart instances
    let scoreTrendChart = null;
    let monthlyChart = null;

    // Elements
    const userNameEl = document.getElementById('user-name');
    const userEmailEl = document.getElementById('user-email');
    const userInitialsEl = document.getElementById('user-initials');
    const logoutBtn = document.getElementById('logout-btn');
    const sidebarLinks = document.querySelectorAll('.sidebar-link');
    const pageTitle = document.getElementById('page-title');

    const pages = {
        dashboard: document.getElementById('dashboard-content'),
        assessment: document.getElementById('assessment-page'),
        history: document.getElementById('history-page'),
        analytics: document.getElementById('analytics-page'),
        profile: document.getElementById('profile-page')
    };

    // Show user info
    const user = auth.getCurrentUser();
    if (user) {
        userNameEl.textContent = `${user.firstName} ${user.lastName}`;
        userEmailEl.textContent = user.email;
        userInitialsEl.textContent = Utils.getInitials(`${user.firstName} ${user.lastName}`);
    } else {
        userNameEl.textContent = 'Demo User';
        userEmailEl.textContent = '<EMAIL>';
        userInitialsEl.textContent = 'DU';
    }

    // Sidebar navigation
    sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = link.getAttribute('data-page');
            if (!page || !pages[page]) return;

            // Update active link
            sidebarLinks.forEach(l => l.classList.remove('sidebar-active'));
            link.classList.add('sidebar-active');

            // Update page title
            pageTitle.textContent = link.textContent.trim();

            // Show selected page, hide others
            Object.entries(pages).forEach(([key, el]) => {
                if (key === page) {
                    el.classList.remove('hidden');
                } else {
                    el.classList.add('hidden');
                }
            });

            // Load page-specific data
            if (page === 'dashboard') {
                loadDashboardData();
            }
        });
    });

    // Logout button
    logoutBtn.addEventListener('click', () => {
        auth.logout();
    });

    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobile-overlay');

    mobileMenuBtn.addEventListener('click', () => {
        sidebar.classList.toggle('mobile-menu-open');
        sidebar.classList.toggle('mobile-menu-closed');
        mobileOverlay.classList.toggle('hidden');
    });

    mobileOverlay.addEventListener('click', () => {
        sidebar.classList.add('mobile-menu-closed');
        sidebar.classList.remove('mobile-menu-open');
        mobileOverlay.classList.add('hidden');
    });

    // Load user role and permissions
    async function loadUserRole() {
        try {
            const response = await Utils.apiCall('/user/role');
            if (response.success) {
                window.userRole = response.data;
                setupRoleBasedUI(response.data);
                return response.data;
            }
        } catch (error) {
            console.error('Failed to load user role:', error);
        }
        return null;
    }

    // Setup UI based on user role
    function setupRoleBasedUI(roleData) {
        const { role, user } = roleData;

        // Check if we should redirect to role-specific dashboard
        const currentPath = window.location.pathname;
        if (currentPath === '/dashboard') {
            switch (role.name) {
                case 'admin':
                    window.location.href = '/admin/dashboard';
                    return;
                case 'guru':
                    window.location.href = '/guru/dashboard';
                    return;
                case 'orangtua':
                    window.location.href = '/orangtua/dashboard';
                    return;
                case 'siswa':
                    // Siswa tetap di dashboard utama
                    break;
            }
        }

        // Update header with role info
        const headerRole = document.querySelector('.text-purple-200');
        if (headerRole) {
            headerRole.textContent = `${role.display_name} - ${user.first_name} ${user.last_name}`;
        }

        // Setup role-specific menu
        setupRoleSpecificMenu(role);

        // Hide/show menu items based on role
        setupMenuPermissions(role);
    }

    // Setup role-specific menu items
    function setupRoleSpecificMenu(role) {
        const roleMenuContainer = document.getElementById('role-specific-menu');
        if (!roleMenuContainer) return;

        let menuItems = [];

        switch (role.name) {
            case 'admin':
                menuItems = [
                    { name: 'Kelola Pengguna', icon: '👥', page: 'users' },
                    { name: 'Kelola Assessment', icon: '📋', page: 'manage-assessments' },
                    { name: 'Laporan', icon: '📊', page: 'reports' },
                    { name: 'Pengaturan', icon: '⚙️', page: 'settings' }
                ];
                break;
            case 'guru':
                menuItems = [
                    { name: 'Daftar Siswa', icon: '👨‍🎓', page: 'students' },
                    { name: 'Hasil Assessment', icon: '📋', page: 'student-assessments' },
                    { name: 'Laporan Kelas', icon: '📊', page: 'class-reports' }
                ];
                break;
            case 'orangtua':
                menuItems = [
                    { name: 'Anak Saya', icon: '👶', page: 'children' },
                    { name: 'Hasil Assessment', icon: '📋', page: 'child-assessments' }
                ];
                break;
            case 'siswa':
                // Siswa hanya memiliki menu default
                break;
        }

        const menuHTML = menuItems.map(item => `
            <a href="#" data-page="${item.page}" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                <span class="w-5 h-5 mr-3 text-center">${item.icon}</span>
                ${item.name}
            </a>
        `).join('');

        roleMenuContainer.innerHTML = menuHTML;
    }

    // Setup menu permissions based on role
    function setupMenuPermissions(role) {
        const assessmentMenu = document.getElementById('assessment-menu');
        const historyMenu = document.getElementById('history-menu');

        // Only siswa can access assessments
        if (role.name !== 'siswa' && assessmentMenu) {
            assessmentMenu.style.display = 'none';
        }

        // Adjust history menu based on role
        if (historyMenu && role.name !== 'siswa') {
            historyMenu.querySelector('span').textContent = 'Hasil Assessment';
        }
    }

    // Dashboard data loading (with mock data for testing)
    async function loadDashboardData() {
        try {
            // Use mock data for testing
            const mockData = {
                total_assessments: 15,
                latest_assessment: {
                    total_score: 4,
                    score: 4,
                    status: 'normal'
                },
                monthly_stats: {
                    total_assessments: 5,
                    normal: 12,
                    concerns: 2,
                    high_risk: 1
                },
                trend: [
                    { month: 'Jan', average_score: 3 },
                    { month: 'Feb', average_score: 5 },
                    { month: 'Mar', average_score: 2 },
                    { month: 'Apr', average_score: 4 },
                    { month: 'May', average_score: 3 },
                    { month: 'Jun', average_score: 4 }
                ],
                recent_activity: [
                    { date: '2024-01-15', type: 'assessment', description: 'SRQ-20 Assessment completed' },
                    { date: '2024-01-10', type: 'consultation', description: 'Consultation with counselor' },
                    { date: '2024-01-05', type: 'assessment', description: 'Mental health check-up' }
                ]
            };

            // Update stats cards
            const totalEl = document.getElementById('total-assessments');
            const scoreEl = document.getElementById('latest-score');
            const statusEl = document.getElementById('latest-status');
            const monthlyEl = document.getElementById('monthly-count');

            if (totalEl) totalEl.textContent = mockData.total_assessments || 0;
            if (scoreEl) scoreEl.textContent = mockData.latest_assessment?.total_score || mockData.latest_assessment?.score || '-';
            if (statusEl) statusEl.textContent = getStatusText(mockData.latest_assessment?.status);
            if (monthlyEl) monthlyEl.textContent = mockData.monthly_stats?.total_assessments || 0;

            // Update status color
            if (statusEl) {
                const status = mockData.latest_assessment?.status;
                statusEl.className = `text-xl font-semibold ${getStatusColor(status)}`;
            }

            // Load charts
            loadScoreTrendChart(mockData.trend || []);
            loadMonthlyChart(mockData.monthly_stats || {});
            loadRecentActivity(mockData.recent_activity || []);

            // Show success notification
            if (window.modernComponents) {
                setTimeout(() => {
                    window.modernComponents.showNotification(
                        'Dashboard loaded successfully!',
                        'success',
                        3000,
                        {
                            description: 'Mock data loaded for testing purposes'
                        }
                    );
                }, 1500);
            }

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            if (window.modernComponents) {
                window.modernComponents.showNotification('Failed to load dashboard data', 'error');
            }
        }
    }



    function getStatusText(status) {
        const statusMap = {
            'normal': 'Normal',
            'concern': 'Perlu Perhatian',
            'high_risk': 'Risiko Tinggi'
        };
        return statusMap[status] || 'Belum Ada Data';
    }

    function getStatusColor(status) {
        const colorMap = {
            'normal': 'text-green-400',
            'concern': 'text-yellow-400',
            'high_risk': 'text-red-400'
        };
        return colorMap[status] || 'text-gray-400';
    }

    // Modern Chart functions
    function loadScoreTrendChart(trendData) {
        const ctx = document.getElementById('score-trend-chart');
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (scoreTrendChart) {
            scoreTrendChart.destroy();
        }

        // Create gradient
        const gradient = ctx.getContext('2d').createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(14, 165, 233, 0.3)');
        gradient.addColorStop(0.5, 'rgba(217, 70, 239, 0.2)');
        gradient.addColorStop(1, 'rgba(14, 165, 233, 0.05)');

        scoreTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.month || Utils.formatDate(item.date, { month: 'short', day: 'numeric' })),
                datasets: [{
                    label: 'Skor SRQ-20',
                    data: trendData.map(item => item.average_score || item.score || 0),
                    borderColor: '#0ea5e9',
                    backgroundColor: gradient,
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#0ea5e9',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 3,
                    pointRadius: 6,
                    pointHoverRadius: 10,
                    pointHoverBackgroundColor: '#d946ef',
                    pointHoverBorderColor: '#ffffff',
                    pointHoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(15, 23, 42, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#cbd5e1',
                        borderColor: '#0ea5e9',
                        borderWidth: 1,
                        cornerRadius: 12,
                        displayColors: false,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        callbacks: {
                            title: function(context) {
                                return `Periode: ${context[0].label}`;
                            },
                            label: function(context) {
                                return `Skor: ${context.parsed.y}/20`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 20,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            font: {
                                size: 12
                            },
                            padding: 10
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            font: {
                                size: 12
                            },
                            padding: 10
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    function loadMonthlyChart(monthlyData) {
        const ctx = document.getElementById('monthly-chart');
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (monthlyChart) {
            monthlyChart.destroy();
        }

        monthlyChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Normal', 'Perlu Perhatian', 'Risiko Tinggi'],
                datasets: [{
                    data: [
                        monthlyData.normal || 0,
                        monthlyData.concerns || 0,
                        monthlyData.high_risk || 0
                    ],
                    backgroundColor: [
                        '#22c55e',
                        '#eab308',
                        '#ef4444'
                    ],
                    borderColor: [
                        '#ffffff',
                        '#ffffff',
                        '#ffffff'
                    ],
                    borderWidth: 3,
                    hoverBackgroundColor: [
                        '#16a34a',
                        '#ca8a04',
                        '#dc2626'
                    ],
                    hoverBorderWidth: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'rgba(255, 255, 255, 0.8)',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(15, 23, 42, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#cbd5e1',
                        borderColor: '#0ea5e9',
                        borderWidth: 1,
                        cornerRadius: 12,
                        displayColors: true,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    function loadRecentActivity(activities) {
        const container = document.getElementById('recent-activity');
        if (!container) return;

        if (activities.length === 0) {
            container.innerHTML = '<p class="text-purple-200 text-center">Belum ada aktivitas terbaru</p>';
            return;
        }

        container.innerHTML = activities.map(activity => `
            <div class="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <div>
                        <p class="text-white font-medium">Skrining SRQ-20 Selesai</p>
                        <p class="text-purple-200 text-sm">Skor: ${activity.score} - ${getStatusText(activity.status)}</p>
                    </div>
                </div>
                <span class="text-purple-300 text-sm">${Utils.formatDate(activity.created_at)}</span>
            </div>
        `).join('');
    }

    // Initialize dashboard with role-based setup
    async function initDashboard() {
        await loadUserRole();
        await loadDashboardData();
    }

    // Load initial dashboard data
    initDashboard();
});
