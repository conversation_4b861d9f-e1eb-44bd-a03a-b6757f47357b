<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FormTemplate;

class FormTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // SRQ-20 (Self Report Questionnaire 20)
        FormTemplate::updateOrCreate(
            ['code' => 'SRQ20'],
            [
            'code' => 'SRQ20',
            'name' => 'Self Report Questionnaire 20',
            'description' => 'Kuesioner skrining kesehatan mental yang dikembangkan oleh WHO untuk mendeteksi gangguan mental umum dalam 30 hari terakhir.',
            'category' => 'mental_health_screening',
            'questions' => [
                'Apakah Anda sering merasa sakit kepala?',
                'Apakah nafsu makan Anda buruk?',
                'Apakah Anda tidur tidak nyenyak?',
                'Apakah Anda mudah merasa takut?',
                '<PERSON>pakah Anda merasa cemas, tegang, atau khawatir?',
                '<PERSON><PERSON>kah tangan Anda gemetar?',
                '<PERSON><PERSON><PERSON>h pencernaan Anda terganggu atau buruk?',
                'Apakah Anda sulit berpikir jernih?',
                'Apakah Anda merasa tidak bahagia?',
                'Apakah Anda lebih sering menangis?',
                'Apakah Anda merasa sulit untuk menikmati kegiatan sehari-hari?',
                'Apakah Anda mengalami kesulitan dalam mengambil keputusan?',
                'Apakah pekerjaan sehari-hari Anda terganggu?',
                'Apakah Anda tidak mampu melakukan hal-hal yang bermanfaat dalam hidup?',
                'Apakah Anda kehilangan minat pada berbagai hal?',
                'Apakah Anda merasa tidak berharga?',
                'Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?',
                'Apakah Anda merasa lelah sepanjang waktu?',
                'Apakah Anda merasa tidak enak di perut?',
                'Apakah Anda mudah lelah?'
            ],
            'scoring_rules' => [
                'type' => 'binary_sum',
                'max_score' => 20,
                'questions' => array_fill(1, 20, [
                    'type' => 'binary',
                    'yes_score' => 1,
                    'no_score' => 0
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 0,
                    'max_score' => 5,
                    'status' => 'normal',
                    'interpretation' => 'Kondisi Kesehatan Mental Anda Baik',
                    'recommendations' => [
                        'Pertahankan pola hidup sehat yang sudah baik',
                        'Lakukan aktivitas yang Anda nikmati secara rutin',
                        'Jaga hubungan sosial yang positif',
                        'Praktikkan mindfulness atau meditasi',
                        'Lakukan skrining berkala untuk monitoring'
                    ]
                ],
                [
                    'min_score' => 6,
                    'max_score' => 7,
                    'status' => 'concern',
                    'interpretation' => 'Terindikasi Mengalami Gangguan Emosional Ringan',
                    'recommendations' => [
                        'Pertimbangkan konsultasi dengan psikolog',
                        'Praktikkan teknik relaksasi dan manajemen stres',
                        'Jaga pola hidup sehat dan olahraga teratur',
                        'Cari dukungan dari keluarga dan teman terdekat',
                        'Monitor kondisi Anda dalam beberapa minggu ke depan'
                    ]
                ],
                [
                    'min_score' => 8,
                    'max_score' => 20,
                    'status' => 'high_risk',
                    'interpretation' => 'Terindikasi Mengalami Gangguan Mental yang Signifikan',
                    'recommendations' => [
                        'Segera konsultasi dengan psikolog atau psikiater',
                        'Pertimbangkan untuk mendapatkan dukungan dari keluarga dan teman',
                        'Hindari penggunaan alkohol atau zat terlarang',
                        'Jaga pola tidur dan makan yang teratur',
                        'Lakukan aktivitas fisik ringan secara rutin'
                    ]
                ]
            ],
            'time_limit' => 15,
            'is_active' => true,
            'version' => 1
            ]
        );

        // GSE (General Self-Efficacy Scale) - Updated with correct questions
        FormTemplate::updateOrCreate(
            ['code' => 'GSE'],
            [
            'code' => 'GSE',
            'name' => 'General Self-Efficacy Scale',
            'description' => 'Skala untuk mengukur keyakinan diri seseorang dalam menghadapi berbagai situasi sulit.',
            'category' => 'self_efficacy',
            'questions' => [
                'Saya bisa menghadapi masalah dengan tenang.',
                'Jika saya mengalami masalah psikologis, saya tahu cara mendapatkan bantuan.',
                'Saya yakin mampu menyelesaikan masalah yang sulit.',
                'Saya merasa yakin untuk menghadapi tantangan hidup.',
                'Saya bisa mencari pertolongan ketika merasa tertekan.',
                'Saya percaya pada kemampuan saya sendiri.',
                'Saya bisa mengambil keputusan dalam situasi yang tidak menentu.',
                'Saya merasa mampu mencari dukungan dari teman atau keluarga saat butuh.',
                'Saya yakin bisa menghadapi masa depan dengan baik.',
                'Saya mampu mengatasi tekanan dari lingkungan sekitar.'
            ],
            'scoring_rules' => [
                'type' => 'likert_sum',
                'max_score' => 40,
                'questions' => array_fill(1, 10, [
                    'type' => 'scale',
                    'min' => 1,
                    'max' => 4,
                    'multiplier' => 1
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 10,
                    'max_score' => 24,
                    'status' => 'low',
                    'interpretation' => 'Self-Efficacy Rendah',
                    'recommendations' => [
                        'Fokus pada pencapaian kecil yang dapat dicapai',
                        'Cari dukungan dari orang terdekat',
                        'Pelajari keterampilan baru secara bertahap',
                        'Praktikkan self-talk yang positif',
                        'Pertimbangkan konseling untuk meningkatkan kepercayaan diri'
                    ]
                ],
                [
                    'min_score' => 25,
                    'max_score' => 29,
                    'status' => 'moderate',
                    'interpretation' => 'Self-Efficacy Cukup',
                    'recommendations' => [
                        'Terus kembangkan kepercayaan diri',
                        'Tantang diri dengan tujuan yang lebih besar',
                        'Refleksikan pencapaian yang sudah diraih',
                        'Belajar dari pengalaman orang lain'
                    ]
                ],
                [
                    'min_score' => 30,
                    'max_score' => 34,
                    'status' => 'high',
                    'interpretation' => 'Self-Efficacy Tinggi',
                    'recommendations' => [
                        'Pertahankan kepercayaan diri yang baik',
                        'Bantu orang lain mengembangkan kepercayaan diri',
                        'Ambil tantangan yang lebih kompleks',
                        'Jadilah mentor bagi orang lain'
                    ]
                ],
                [
                    'min_score' => 35,
                    'max_score' => 40,
                    'status' => 'very_high',
                    'interpretation' => 'Self-Efficacy Sangat Tinggi',
                    'recommendations' => [
                        'Anda memiliki keyakinan yang sangat kuat pada kemampuan diri',
                        'Terus jadi inspirasi bagi orang lain',
                        'Ambil peran kepemimpinan dalam tantangan besar',
                        'Bagikan pengalaman dan strategi sukses Anda'
                    ]
                ]
            ],
            'time_limit' => 10,
            'is_active' => true,
            'version' => 1
            ]
        );

        // MSCS (Multidimensional Scale of Perceived Social Support)
        FormTemplate::updateOrCreate(
            ['code' => 'MSCS'],
            [
            'code' => 'MSCS',
            'name' => 'Multidimensional Scale of Perceived Social Support',
            'description' => 'Skala untuk mengukur persepsi dukungan sosial dari keluarga, teman, dan orang penting lainnya.',
            'category' => 'social_support',
            'questions' => [
                'Ada orang khusus yang ada saat saya membutuhkan',
                'Ada orang khusus yang dapat saya bagi kegembiraan dan kesedihan',
                'Keluarga saya benar-benar mencoba membantu saya',
                'Saya mendapat dukungan emosional yang saya butuhkan dari keluarga',
                'Saya memiliki orang khusus yang merupakan sumber kenyamanan bagi saya',
                'Teman-teman saya benar-benar mencoba membantu saya',
                'Saya dapat mengandalkan teman-teman saya ketika ada masalah',
                'Saya dapat membicarakan masalah saya dengan keluarga',
                'Saya memiliki teman yang dapat saya bagi kegembiraan dan kesedihan',
                'Ada orang khusus dalam hidup saya yang peduli dengan perasaan saya',
                'Keluarga saya bersedia membantu saya membuat keputusan',
                'Saya dapat membicarakan masalah saya dengan teman-teman'
            ],
            'scoring_rules' => [
                'type' => 'likert_sum',
                'max_score' => 84,
                'questions' => array_fill(1, 12, [
                    'type' => 'scale',
                    'min' => 1,
                    'max' => 7,
                    'multiplier' => 1
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 12,
                    'max_score' => 36,
                    'status' => 'low',
                    'interpretation' => 'Dukungan Sosial Rendah',
                    'recommendations' => [
                        'Coba untuk lebih terbuka dengan orang terdekat',
                        'Bergabung dengan komunitas atau kelompok sosial',
                        'Pertimbangkan konseling untuk meningkatkan keterampilan sosial',
                        'Mulai membangun hubungan yang lebih bermakna'
                    ]
                ],
                [
                    'min_score' => 37,
                    'max_score' => 60,
                    'status' => 'moderate',
                    'interpretation' => 'Dukungan Sosial Sedang',
                    'recommendations' => [
                        'Pertahankan hubungan yang sudah ada',
                        'Coba untuk lebih aktif dalam interaksi sosial',
                        'Ekspresikan kebutuhan dukungan dengan jelas',
                        'Berikan dukungan kepada orang lain juga'
                    ]
                ],
                [
                    'min_score' => 61,
                    'max_score' => 84,
                    'status' => 'high',
                    'interpretation' => 'Dukungan Sosial Tinggi',
                    'recommendations' => [
                        'Pertahankan jaringan dukungan yang kuat',
                        'Jadilah sumber dukungan bagi orang lain',
                        'Syukuri hubungan yang bermakna dalam hidup',
                        'Terus jaga komunikasi yang baik dengan orang terdekat'
                    ]
                ]
            ],
            'time_limit' => 8,
            'is_active' => true,
            'version' => 1
            ]
        );

        // MHKQ (Mental Health Knowledge Questionnaire)
        FormTemplate::updateOrCreate(
            ['code' => 'MHKQ'],
            [
            'code' => 'MHKQ',
            'name' => 'Mental Health Knowledge Questionnaire',
            'description' => 'Kuesioner untuk mengukur tingkat pengetahuan tentang kesehatan jiwa dan gangguan mental.',
            'category' => 'mental_health_knowledge',
            'questions' => [
                'Kesehatan jiwa berarti kondisi seseorang dapat mengenali potensi dirinya.',
                'Orang dengan gangguan jiwa tidak bisa hidup mandiri.',
                'Gangguan jiwa seperti depresi bisa menyerang siapa saja.',
                'Semua orang dengan gangguan jiwa akan melakukan kekerasan.',
                'Faktor sosial dan ekonomi bisa mempengaruhi kesehatan jiwa.',
                'Gangguan jiwa tidak dapat disembuhkan.',
                'Kecemasan berlebihan termasuk bentuk gangguan jiwa.',
                'Masalah kejiwaan hanya dialami orang dengan latar belakang keluarga miskin.',
                'Kesehatan fisik yang buruk bisa berdampak pada kesehatan jiwa.',
                'Gangguan jiwa tidak bisa dicegah.',
                'Orang dengan gangguan jiwa sering mengalami diskriminasi.',
                'Konsumsi narkoba dapat memicu gangguan kesehatan jiwa.',
                'Ada bantuan profesional untuk masalah kesehatan jiwa di puskesmas.',
                'Mencari bantuan psikolog berarti seseorang pasti gila.',
                'Mendapatkan informasi tentang kesehatan jiwa bisa membantu pencegahan.',
                'Orang pintar (dukun) lebih ampuh menyembuhkan gangguan jiwa daripada dokter.',
                'Konseling adalah bagian dari promosi kesehatan jiwa.',
                'Sekolah dapat menjadi tempat edukasi tentang kesehatan jiwa.',
                'Film animasi bisa digunakan sebagai media edukasi kesehatan jiwa.',
                'Promosi kesehatan jiwa hanya penting bagi orang yang sudah sakit jiwa.'
            ],
            'scoring_rules' => [
                'type' => 'knowledge_sum',
                'max_score' => 20,
                'answer_key' => [
                    1 => 'benar', 2 => 'salah', 3 => 'benar', 4 => 'salah', 5 => 'benar',
                    6 => 'salah', 7 => 'benar', 8 => 'salah', 9 => 'benar', 10 => 'salah',
                    11 => 'benar', 12 => 'benar', 13 => 'benar', 14 => 'salah', 15 => 'benar',
                    16 => 'salah', 17 => 'benar', 18 => 'benar', 19 => 'benar', 20 => 'salah'
                ],
                'questions' => array_fill(1, 20, [
                    'type' => 'true_false',
                    'correct_score' => 1,
                    'incorrect_score' => 0
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 0,
                    'max_score' => 10,
                    'status' => 'low',
                    'interpretation' => 'Pengetahuan Kesehatan Jiwa Rendah',
                    'recommendations' => [
                        'Tidak apa-apa, belajar adalah sebuah proses',
                        'Mulai memperdalam pengetahuan tentang kesehatan jiwa',
                        'Baca artikel dan buku tentang kesehatan mental',
                        'Ikuti seminar atau workshop kesehatan jiwa',
                        'Konsultasi dengan profesional kesehatan mental'
                    ]
                ],
                [
                    'min_score' => 11,
                    'max_score' => 15,
                    'status' => 'moderate',
                    'interpretation' => 'Pengetahuan Kesehatan Jiwa Cukup',
                    'recommendations' => [
                        'Anda sudah memiliki dasar pengetahuan yang baik',
                        'Terus belajar untuk lebih memahami kesehatan jiwa',
                        'Bagikan informasi yang benar kepada orang lain',
                        'Ikuti perkembangan terbaru dalam bidang kesehatan mental',
                        'Dukung kampanye anti-stigma kesehatan mental'
                    ]
                ],
                [
                    'min_score' => 16,
                    'max_score' => 20,
                    'status' => 'high',
                    'interpretation' => 'Pengetahuan Kesehatan Jiwa Tinggi',
                    'recommendations' => [
                        'Luar biasa! Pengetahuan Anda sangat baik',
                        'Teruslah menjadi agen perubahan',
                        'Sebarkan informasi yang akurat tentang kesehatan jiwa',
                        'Bantu mengurangi stigma terhadap gangguan mental',
                        'Pertimbangkan untuk menjadi volunteer atau advocate'
                    ]
                ]
            ],
            'time_limit' => 15,
            'is_active' => true,
            'version' => 1
            ]
        );

        // DASS42 (Depression, Anxiety and Stress Scale)
        FormTemplate::updateOrCreate(
            ['code' => 'DASS42'],
            [
            'code' => 'DASS42',
            'name' => 'Depression, Anxiety and Stress Scale (DASS-42)',
            'description' => 'Skala untuk mengukur tingkat depresi, kecemasan, dan stres dalam satu minggu terakhir.',
            'category' => 'mental_health_assessment',
            'questions' => [
                'Saya marah karena hal-hal sepele.',
                'Bibir kering.',
                'Saya sama sekali tidak dapat merasakan perasaan positif.',
                'Saya merasa sesak nafas (misalnya: seringkali terengah-engah atau tidak dapat bernafas padahal tidak melakukan aktivitas fisik sebelumnya).',
                'Saya sulit untuk bersantai.',
                'Saya cenderung tidak sabar ketika mengalami penundaan (misalnya: kemacetan lalu lintas, menunggu sesuatu).',
                'Saya merasa gemetar (misalnya: pada tangan).',
                'Saya merasa sulit beristirahat.',
                'Saya merasa khawatir dengan keadaan yang membuat saya panik.',
                'Saya merasa bahwa hidup tidak bermanfaat.',
                'Saya cenderung bersikap berlebihan terhadap suatu keadaan.',
                'Saya sulit bersabar dalam menghadapi gangguan.',
                'Saya merasa sedih dan tertekan.',
                'Saya merasa mudah tersinggung.',
                'Saya merasa saya hampir panik.',
                'Saya merasa bahwa saya tidak berharga.',
                'Saya merasa kehilangan minat akan segala hal.',
                'Saya merasa mudah marah.',
                'Saya merasakan denyut jantung saya sendiri (misalnya: merasa detak jantung meningkat atau melemah).',
                'Saya takut tanpa alasan yang jelas.',
                'Saya merasa bahwa saya tidak berharga sebagai seorang manusia.'
            ],
            'scoring_rules' => [
                'type' => 'dass_scale',
                'max_score' => 126, // 42 questions x 3 max score
                'subscales' => [
                    'depression' => [3, 5, 10, 13, 16, 17, 21, 24, 26, 31, 34, 37, 38, 42],
                    'anxiety' => [2, 4, 7, 9, 15, 19, 20, 23, 25, 28, 30, 36, 40, 41],
                    'stress' => [1, 6, 8, 11, 12, 14, 18, 22, 27, 29, 32, 33, 35, 39]
                ],
                'questions' => array_fill(1, 21, [ // Only showing first 21 for demo
                    'type' => 'scale',
                    'min' => 0,
                    'max' => 3,
                    'multiplier' => 1
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 0,
                    'max_score' => 30,
                    'status' => 'normal',
                    'interpretation' => 'Tingkat Normal',
                    'recommendations' => [
                        'Kondisi psikologis Anda dalam rentang normal',
                        'Pertahankan pola hidup sehat',
                        'Lakukan aktivitas yang menyenangkan secara rutin',
                        'Jaga keseimbangan work-life balance',
                        'Tetap waspada terhadap perubahan mood'
                    ]
                ],
                [
                    'min_score' => 31,
                    'max_score' => 60,
                    'status' => 'mild',
                    'interpretation' => 'Tingkat Ringan',
                    'recommendations' => [
                        'Mulai perhatikan pola pikir dan perasaan Anda',
                        'Praktikkan teknik relaksasi dan mindfulness',
                        'Tingkatkan aktivitas fisik dan olahraga',
                        'Cari dukungan dari keluarga dan teman',
                        'Pertimbangkan konseling jika diperlukan'
                    ]
                ],
                [
                    'min_score' => 61,
                    'max_score' => 90,
                    'status' => 'moderate',
                    'interpretation' => 'Tingkat Sedang',
                    'recommendations' => [
                        'Disarankan untuk berkonsultasi dengan psikolog',
                        'Pelajari teknik manajemen stres yang efektif',
                        'Atur jadwal istirahat yang cukup',
                        'Hindari alkohol dan zat adiktif lainnya',
                        'Pertimbangkan terapi kognitif behavioral'
                    ]
                ],
                [
                    'min_score' => 91,
                    'max_score' => 126,
                    'status' => 'severe',
                    'interpretation' => 'Tingkat Berat',
                    'recommendations' => [
                        'Segera konsultasi dengan psikolog atau psikiater',
                        'Pertimbangkan terapi profesional',
                        'Jangan ragu untuk meminta bantuan keluarga',
                        'Hindari keputusan penting saat kondisi tidak stabil',
                        'Ikuti program terapi yang direkomendasikan'
                    ]
                ]
            ],
            'time_limit' => 20,
            'is_active' => true,
            'version' => 1
            ]
        );
    }
}
