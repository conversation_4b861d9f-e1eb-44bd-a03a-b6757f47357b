@extends('tailadmin-base')

@section('title', 'Riwayat Skrining - SantriMental')
@section('dashboard-subtitle', 'History Center')
@section('page-title', 'Riwayat Skrining')
@section('page-description', '<PERSON>hat semua hasil skrining kesehatan mental Anda dan pantau perkembangan dari waktu ke waktu')
@section('user-initials', 'SS')
@section('user-name', 'Siswa Demo')
@section('user-role', 'Student')

@section('logo-icon')
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
</svg>
@endsection

@section('navigation')
<div class="mb-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6"><PERSON><PERSON></p>

    <a href="{{ route('dashboard') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="font-medium">Dashboard</span>
    </a>

    <a href="{{ route('assessments') }}" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
        </svg>
        <span class="font-medium">Skrining Mental</span>
    </a>

    <a href="{{ route('history') }}" class="tailadmin-nav-item active">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Riwayat Tes</span>
        <div class="ml-auto">
            <span class="tailadmin-badge success text-xs">Aktif</span>
        </div>
    </a>
</div>
@endsection

@section('header-actions')
<button class="tailadmin-btn tailadmin-btn-success" onclick="window.location.href='{{ route('assessments') }}'">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Skrining Baru
</button>

<button class="tailadmin-btn tailadmin-btn-outline" onclick="exportHistory()">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    Export
</button>
@endsection

@section('breadcrumb')
<a href="{{ route('dashboard') }}" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Dashboard</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<span class="tailadmin-text-primary font-medium">Riwayat Skrining</span>
@endsection

@section('content')
<!-- Statistics Summary -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.1s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon primary">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +3
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Total Skrining</p>
            <p class="tailadmin-stats-value" id="total-count">0</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Tes selesai</p>
        </div>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.2s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon success">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Baik
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Rata-rata Skor</p>
            <p class="tailadmin-stats-value" id="avg-score">0</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Dari 20 poin</p>
        </div>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.3s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon warning">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="w-3 h-3 bg-success-500 rounded-full animate-pulse"></div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Skor Terakhir</p>
            <p class="text-2xl font-bold text-success-500 mb-1" id="latest-score">-</p>
            <p class="text-xs tailadmin-text-secondary">Status normal</p>
        </div>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.4s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon secondary">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10a2 2 0 002 2h4a2 2 0 002-2V11m-6 0h6"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-primary-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +2
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Bulan Ini</p>
            <p class="tailadmin-stats-value" id="this-month">0</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Skrining selesai</p>
        </div>
    </div>
</div>

<!-- History List -->
<div class="tailadmin-card mb-8">
    <div class="tailadmin-card-header">
        <div class="flex items-center justify-between">
            <h3 class="tailadmin-card-title">Riwayat Skrining</h3>
            <div class="flex items-center space-x-4">
                <select id="filter-status" class="bg-gray-100 dark:bg-dark-200 border border-gray-200 dark:border-dark-300 rounded-lg px-3 py-2 tailadmin-text-primary text-sm">
                    <option value="">Semua Status</option>
                    <option value="normal">Normal</option>
                    <option value="concern">Perlu Perhatian</option>
                    <option value="high_risk">Risiko Tinggi</option>
                </select>
                <select id="filter-period" class="bg-gray-100 dark:bg-dark-200 border border-gray-200 dark:border-dark-300 rounded-lg px-3 py-2 tailadmin-text-primary text-sm">
                    <option value="">Semua Periode</option>
                    <option value="week">Minggu Ini</option>
                    <option value="month">Bulan Ini</option>
                    <option value="3months">3 Bulan Terakhir</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loading-state" class="text-center py-12">
        <div class="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p class="tailadmin-text-secondary">Memuat riwayat...</p>
    </div>

    <!-- Empty State -->
    <div id="empty-state" class="hidden text-center py-12">
        <div class="w-16 h-16 bg-gray-100 dark:bg-dark-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 tailadmin-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-semibold tailadmin-text-primary mb-2">Belum Ada Riwayat</h3>
        <p class="tailadmin-text-secondary mb-4">Anda belum pernah melakukan skrining kesehatan mental</p>
        <button class="tailadmin-btn tailadmin-btn-primary" onclick="window.location.href='{{ route('assessments') }}'">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Mulai Skrining Pertama
        </button>
    </div>

    <!-- History Items -->
    <div id="history-list" class="hidden space-y-4">
        <!-- History items will be inserted here -->
    </div>

    <!-- Pagination -->
    <div id="pagination" class="hidden flex items-center justify-center mt-6 space-x-2">
        <!-- Pagination will be inserted here -->
    </div>
</div>

<!-- Progress Chart -->
<div class="tailadmin-card">
    <div class="tailadmin-card-header">
        <h3 class="tailadmin-card-title">Grafik Perkembangan</h3>
    </div>
    <div class="h-64" id="progress-chart-container">
        <canvas id="progress-chart"></canvas>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', async () => {
    const loadingState = document.getElementById('loading-state');
    const historyList = document.getElementById('history-list');
    const emptyState = document.getElementById('empty-state');

    try {
        // Simulate loading history (replace with actual API call)
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock history data
        const historyData = [
            {
                id: 1,
                assessment_type: 'SRQ-20',
                score: 4,
                max_score: 20,
                status: 'normal',
                date: '2024-12-15',
                time: '14:30',
                interpretation: 'Kondisi mental dalam batas normal'
            },
            {
                id: 2,
                assessment_type: 'SRQ-20',
                score: 8,
                max_score: 20,
                status: 'concern',
                date: '2024-12-10',
                time: '10:15',
                interpretation: 'Perlu perhatian lebih lanjut'
            },
            {
                id: 3,
                assessment_type: 'SRQ-20',
                score: 3,
                max_score: 20,
                status: 'normal',
                date: '2024-12-05',
                time: '16:45',
                interpretation: 'Kondisi mental sangat baik'
            }
        ];

        if (historyData.length > 0) {
            displayHistory(historyData);
            updateStats(historyData);
            createProgressChart(historyData);
        } else {
            showEmpty();
        }
    } catch (error) {
        console.error('Failed to load history:', error);
        showToast('Gagal memuat riwayat skrining', 'error');
        showEmpty();
    }

    function displayHistory(history) {
        const statusColors = {
            'normal': 'success',
            'concern': 'warning',
            'high_risk': 'danger'
        };

        const statusLabels = {
            'normal': 'Normal',
            'concern': 'Perlu Perhatian',
            'high_risk': 'Risiko Tinggi'
        };

        const historyHTML = history.map((item, index) => {
            const statusColor = statusColors[item.status] || 'primary';
            const statusLabel = statusLabels[item.status] || 'Unknown';
            
            return `
                <div class="tailadmin-card border-l-4 border-${statusColor}-500 tailadmin-fade-in-up" style="animation-delay: ${index * 0.1}s;">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-${statusColor}-100 dark:bg-${statusColor}-900 rounded-xl flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-${statusColor}-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 7l2 2 4-4"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold tailadmin-text-primary">${item.assessment_type}</h4>
                                <p class="tailadmin-text-secondary text-sm">${item.date} • ${item.time}</p>
                                <p class="text-xs tailadmin-text-secondary mt-1">${item.interpretation}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-${statusColor}-500 mb-1">${item.score}/${item.max_score}</div>
                            <span class="tailadmin-badge ${statusColor} text-xs">${statusLabel}</span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-dark-300">
                        <div class="flex space-x-2">
                            <button class="tailadmin-btn tailadmin-btn-outline text-xs px-3 py-2 flex-1" onclick="viewDetails(${item.id})">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                Detail
                            </button>
                            <button class="tailadmin-btn tailadmin-btn-outline text-xs px-3 py-2 flex-1" onclick="downloadReport(${item.id})">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        historyList.innerHTML = historyHTML;
        showHistory();
    }

    function updateStats(history) {
        document.getElementById('total-count').textContent = history.length;
        
        const avgScore = Math.round(history.reduce((sum, item) => sum + item.score, 0) / history.length);
        document.getElementById('avg-score').textContent = avgScore;
        
        const latestScore = history[0]?.score || '-';
        document.getElementById('latest-score').textContent = latestScore;
        
        const thisMonth = history.filter(item => {
            const itemDate = new Date(item.date);
            const now = new Date();
            return itemDate.getMonth() === now.getMonth() && itemDate.getFullYear() === now.getFullYear();
        }).length;
        document.getElementById('this-month').textContent = thisMonth;
    }

    function createProgressChart(history) {
        const ctx = document.getElementById('progress-chart');
        if (ctx && window.Chart) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: history.reverse().map(item => item.date),
                    datasets: [{
                        label: 'Skor SRQ-20',
                        data: history.map(item => item.score),
                        borderColor: '#3c50e0',
                        backgroundColor: 'rgba(60, 80, 224, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#1e293b'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 20,
                            ticks: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff80' : '#64748b'
                            },
                            grid: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff20' : '#e2e8f0'
                            }
                        },
                        x: {
                            ticks: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff80' : '#64748b'
                            },
                            grid: {
                                color: document.documentElement.classList.contains('dark') ? '#ffffff20' : '#e2e8f0'
                            }
                        }
                    }
                }
            });
        }
    }

    function showHistory() {
        loadingState.classList.add('hidden');
        emptyState.classList.add('hidden');
        historyList.classList.remove('hidden');
    }

    function showEmpty() {
        loadingState.classList.add('hidden');
        historyList.classList.add('hidden');
        emptyState.classList.remove('hidden');
    }

    // Global functions
    window.viewDetails = (id) => {
        showToast(`Melihat detail skrining #${id}`, 'info');
    };

    window.downloadReport = (id) => {
        showToast(`Mengunduh laporan #${id}`, 'success');
    };

    window.exportHistory = () => {
        showToast('Mengekspor riwayat skrining...', 'info');
    };
});
</script>
@endpush
