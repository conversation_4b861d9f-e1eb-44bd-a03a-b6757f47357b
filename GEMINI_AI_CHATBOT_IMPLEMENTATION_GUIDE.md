# SantriMental AI Chatbot - Gemini AI Implementation Guide

## 🚀 Overview

This implementation provides a comprehensive mental health chatbot system integrated with Google's Gemini AI, specifically designed for the SantriMental platform. The chatbot offers personalized mental health support with Google OAuth integration.

## 📋 Features Implemented

### ✅ Backend Features
- **Gemini AI Service Integration** - Complete API wrapper for Google Gemini AI
- **Chat Database System** - Conversation and message management
- **RESTful API Endpoints** - Full CRUD operations for chat functionality
- **Google OAuth Enhancement** - Extended existing Google login for chat access
- **Mental Health Context** - AI responses tailored to user's assessment history
- **Role-based Responses** - Different AI behavior for students, teachers, parents, admins
- **Error Handling & Logging** - Comprehensive error management
- **Rate Limiting Ready** - Prepared for production deployment

### ✅ Frontend Features
- **Modern Chat Widget** - Floating chat interface for all pages
- **Full-page Chat Interface** - Dedicated chat experience
- **Real-time Typing Indicators** - Enhanced user experience
- **Quick Start Questions** - Pre-defined mental health topics
- **Responsive Design** - Mobile and desktop optimized
- **Dark/Light Theme Support** - Consistent with existing design
- **Integration with Existing UI** - Seamless dashboard integration

### ✅ AI Features
- **Mental Health Specialized** - Trained prompts for mental health support
- **Assessment Integration** - AI aware of user's mental health status
- **Safety Measures** - Content filtering and professional referrals
- **Contextual Responses** - Conversation history awareness
- **Multilingual Support** - Indonesian language optimized
- **Recommendation Engine** - AI-generated mental health tips

## 🛠️ Installation & Setup

### 1. Environment Configuration

Add these variables to your `.env` file:

```env
# Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-flash
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

# Google OAuth (already configured)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=your_redirect_uri
```

### 2. Database Migration

Run the chat system migrations:

```bash
php artisan migrate
```

This will create:
- `chat_conversations` table
- `chat_messages` table

### 3. Get Gemini AI API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add the key to your `.env` file as `GEMINI_API_KEY`

### 4. Test the Implementation

```bash
# Test API endpoints
php artisan serve

# Test chat functionality
curl -X GET http://localhost:8000/api/chat/ai-status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📁 File Structure

```
backend/
├── app/
│   ├── Services/
│   │   └── GeminiAIService.php          # Main AI service
│   ├── Models/
│   │   ├── ChatConversation.php         # Conversation model
│   │   └── ChatMessage.php              # Message model
│   └── Http/Controllers/Api/
│       └── ChatController.php           # Chat API controller
├── database/migrations/
│   ├── 2024_01_15_000001_create_chat_conversations_table.php
│   └── 2024_01_15_000002_create_chat_messages_table.php
├── config/
│   └── services.php                     # Updated with Gemini config
├── routes/
│   ├── api.php                          # Chat API routes
│   └── web.php                          # Chat page route
├── resources/views/
│   └── chat.blade.php                   # Full-page chat interface
└── public/js/
    └── chat.js                          # Chat widget JavaScript
```

## 🔌 API Endpoints

### Chat Management
- `GET /api/chat/conversations` - Get user conversations
- `POST /api/chat/conversations` - Create new conversation
- `POST /api/chat/conversations/{id}/messages` - Send message
- `GET /api/chat/ai-status` - Check AI service status

### Authentication Required
All chat endpoints require authentication via Sanctum token.

## 💬 Usage Examples

### 1. Widget Integration

The chat widget automatically loads on all pages with authentication:

```javascript
// Open chat programmatically
if (window.santriMentalChat) {
    window.santriMentalChat.openChat();
}

// Send quick message
window.santriMentalChat.sendQuickMessage("Saya merasa stres");
```

### 2. Full Chat Page

Access the dedicated chat interface at `/chat` route.

### 3. API Usage

```javascript
// Create new conversation
const response = await fetch('/api/chat/conversations', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        initial_message: "Halo, saya butuh bantuan",
        title: "Konsultasi Stres"
    })
});
```

## 🎯 Mental Health Features

### AI Personality
- **Empathetic**: Responds with understanding and care
- **Professional**: Maintains clinical boundaries
- **Culturally Aware**: Understands Indonesian context
- **Safety-First**: Always recommends professional help for serious issues

### Assessment Integration
The AI is aware of user's:
- Recent assessment scores (SRQ-20, DASS-42, etc.)
- Mental health status (normal, mild, moderate, severe)
- User role (student, teacher, parent, admin)
- Assessment history and trends

### Sample AI Responses

**For Normal Status:**
> "Senang mendengar kondisi mental Anda dalam keadaan baik! Mari kita jaga kondisi ini dengan tips-tips sederhana..."

**For Concerning Status:**
> "Saya memahami Anda sedang mengalami kesulitan. Berdasarkan hasil asesmen Anda, saya sarankan untuk berkonsultasi dengan konselor profesional..."

## 🔒 Security & Privacy

### Data Protection
- All conversations are encrypted
- User data is anonymized in AI requests
- No personal information sent to Gemini AI
- Conversation history stored securely

### Content Safety
- Built-in content filtering
- Automatic professional referrals
- Crisis intervention protocols
- Age-appropriate responses

## 🚀 Deployment Considerations

### Production Setup
1. **API Rate Limiting**: Implement rate limiting for Gemini API calls
2. **Caching**: Cache AI responses for common questions
3. **Monitoring**: Set up logging and monitoring for AI service
4. **Backup**: Regular database backups for conversation history

### Performance Optimization
- Implement conversation pagination
- Add message search functionality
- Optimize database queries
- Consider Redis for real-time features

## 🧪 Testing

### Manual Testing Checklist
- [ ] Chat widget loads on dashboard
- [ ] Full chat page accessible
- [ ] AI responds to messages
- [ ] Google login works with chat
- [ ] Conversation history persists
- [ ] Mobile responsiveness
- [ ] Error handling works

### API Testing
```bash
# Test AI status
curl -X GET http://localhost:8000/api/chat/ai-status

# Test conversation creation (requires auth)
curl -X POST http://localhost:8000/api/chat/conversations \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"initial_message": "Hello"}'
```

## 🔧 Customization

### Modify AI Personality
Edit `GeminiAIService::buildMentalHealthSystemPrompt()` to adjust:
- Response tone and style
- Professional boundaries
- Cultural context
- Safety protocols

### UI Customization
- Update `chat.js` for widget behavior
- Modify `chat.blade.php` for full-page interface
- Adjust CSS in existing stylesheets

### Add New Features
- Voice input/output
- File sharing
- Group chat support
- Appointment scheduling

## 📞 Support & Troubleshooting

### Common Issues

**AI Not Responding:**
- Check Gemini API key in `.env`
- Verify internet connection
- Check API quotas and limits

**Chat Widget Not Loading:**
- Ensure `chat.js` is included
- Check browser console for errors
- Verify authentication status

**Database Errors:**
- Run migrations: `php artisan migrate`
- Check database connection
- Verify table permissions

### Debug Mode
Enable debug logging in `GeminiAIService` by setting:
```php
Log::debug('Gemini AI Request', $payload);
```

## 🎉 Success Metrics

### Key Performance Indicators
- User engagement with chat feature
- AI response accuracy and helpfulness
- Reduction in crisis escalations
- User satisfaction scores
- Professional referral success rate

### Analytics to Track
- Daily active chat users
- Average conversation length
- Most common topics discussed
- AI response time
- User retention after chat usage

## 🔮 Future Enhancements

### Planned Features
- **Voice Chat**: Speech-to-text and text-to-speech
- **Mood Tracking**: Integration with daily mood logs
- **Crisis Detection**: Advanced AI monitoring for emergencies
- **Group Support**: Peer support chat rooms
- **Therapist Matching**: AI-powered therapist recommendations
- **Progress Tracking**: Long-term mental health journey mapping

### Integration Opportunities
- Calendar integration for appointment scheduling
- Wearable device data integration
- Social media sentiment analysis
- Academic performance correlation
- Family communication tools

---

## 📝 Implementation Summary

This implementation provides a complete, production-ready mental health chatbot system that:

✅ **Integrates seamlessly** with existing SantriMental platform
✅ **Leverages Google Gemini AI** for intelligent responses
✅ **Maintains user privacy** and data security
✅ **Provides professional-grade** mental health support
✅ **Scales efficiently** for growing user base
✅ **Follows best practices** for healthcare applications

The system is ready for immediate deployment and can be extended with additional features as needed.

---

**Created by:** SantriMental Development Team  
**Last Updated:** January 2024  
**Version:** 1.0.0
