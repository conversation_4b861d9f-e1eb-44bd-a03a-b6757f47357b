# Rancangan TOKEN PEDIA

## <PERSON>gorit<PERSON> Aplikasi “TOKEN PEDIA”
Ini adalah rancangan aplikasi TOKEN PEDIA yang dirancang untuk membantu santri dalam meningkatkan kesehatan jiwa dan kesejahteraan. Aplikasi ini akan membantu santri dalam mengidentifikasi masalah kesehatan jiwa, memberikan rekomendasi terapeutik, dan memberikan edukasi terkait kesehatan jiwa. <PERSON><PERSON><PERSON> adalah rancangan aplikasi TOKEN PEDIA:
- **Pengembangan dari pesantren digital sebagai upaya peningkatan kesehatan jiwa dan kesejahteraan santri.**
- **Bentuk:** Aplikasi Android/Webview/Web 
- **Fitur Utama:** Ada Halaman Landing Page, Login, Register, Dashboard, Assessment, History, Analytics, Profile, dan <PERSON>. Halaman untuk pengetahuan mental health nonton Video youtube, game, baca ebook pdf, film edukasi mental health, animasi, dll. 
- **Bagian Utama:** Backend (Codeigniter 4/Laravel 10), Frontend (React Kit, Typescript, Tailwind CSS, Chart.js, Vite), Database (MySQL, or NeonDB, Aiven Postgres, Supabase), dan API (RESTful API). Mobile Apps (Android, iOS) via Flutter atau React Native/Expo 
- **Backend:** Dashboard,CRUDL (Create, Read, Update, Delete, List), WYSIWYG (What You See Is What You Get) for Dynamic Assessment Form Design, Authentication, Assessment, History, Analytics, Profile, Social Login, dan API Management.
- **Frontend:** Landing Page, Login, Register, Dashboard, Assessment, History, Analytics, Profile, dan Logout. 
- **Database:** MySQL, or NeonDB, Aiven Postgres, Supabase.
- **API:** RESTful API.
- **Mobile Apps:** Android, iOS via Flutter atau React Native/Expo.
- **Koneksi:** API, Database, dan Backend.
- **Arsitektur:** Microservices Architecture.
- **Kriteria:** Kriteria aplikasi adalah dapat digunakan oleh santri, dapat membantu santri dalam mengidentifikasi masalah kesehatan jiwa, dapat memberikan rekomendasi terapeutik, dan dapat memberikan edukasi terkait kesehatan jiwa. 



### Bagian-Bagian dari Aplikasi “TOKEN PEDIA”

#### Flowchart Structure: TOKEN PEDIA


[TOKEN PEDIA]
├── Skrining kesehatan jiwa bagi santri terdiri dari:
│   ├── MHKQ (Mental Health Knowledge Questionnaire)
│   │   - Kuesioner untuk mengukur tingkat literasi kesehatan mental seseorang.
│   │   - Link: [MHKQ Document](https://docs.google.com/document/d/1qma5hBfuzQMQqLA-)
│   ├── PDD (Perceived Devaluation Discrimination)
│   │   - Alat ukur untuk menilai keyakinan seseorang bahwa dirinya diperlakukan negatif atau tidak adil.
│   │   - Link: [PDD Document](https://docs.google.com/document/d/1XHCibeQfIneNOLPaNmwMmnuceasnoeWh/edit?usp=sharing)
│   ├── GSE (General Self-Efficacy)
│   │   - Alat ukur untuk menilai keyakinan umum seseorang tentang kemampuannya mengatasi situasi dan tantangan.
│   │   - Link: [GSE Document](https://docs.google.com/document/d/1IwNIrfQpcmV5I0yM-t)
│   ├── MSCS (Mindful Self-Care Scale)
│   │   - Alat ukur untuk mengidentifikasi area kekuatan dan kelemahan dalam perawatan diri.
│   │   - Link: [MSCS Document](https://docs.google.com/document/d/1r5a9ZMIeRr9J-yA8FZJDIjVUx6keWgT-)
│   ├── SRQ 20 (Self-Reporting Questionnaire-20)
│   │   - Alat ukur untuk skrining gangguan psikiatri atau mendeteksi distres emosional.
│   │   - Link: [SRQ 20 Document](https://docs.google.com/document/d/1STjaeoqCylbdQShxkjg9FXYgzROvd1dW/edit?usp=sharing)
│   └── DASS 42
│       - Alat ukur untuk mengidentifikasi depresi, kecemasan, dan stres.
│       - Link: [DASS 42 Document](https://docs.google.com/document/d/13ki5rVNE__H1Fd2UWU3eb_BaTyv6GRVT/edit?usp=sharing&ouid=106103793427421054497&rtpof=true)
├── Terapeutik:
│   ├── Perawatan diri kesehatan jiwa di Pesantren
│   │   - Link Modul Perawatan Diri: [Modul Perawatan Diri](https://drive.google.com/file/d/1tyJU--saXDw5gARV7msA4voQCziVxD4W/view?usp=sharing)
│   └── Modifikasi perilaku
│       - (Rata-rata aktivitas yang dilakukan setiap hari akan menjadi rapot setempat mingguan)
│       - Tabel Modifikasi Perilaku: Hari, Tanggal, Bulan (Note: Data incomplete due to OCR errors)
├── Edukasi/Promosi Kesehatan
│   ├── E-Modul Psikoedukasi Kesehatan jiwa di pondok pesantren
│   │   - Link: [E-Modul Psikoedukasi](https://drive.google.com/file/d/1J9Me3jFdIvwWZFfCzgyoCrHbEk2/view?usp=sharing)
│   ├── Modul digital Perawatan diri kesehatan jiwa di pondok pesantren
│   │   - Link: [Modul Digital Perawatan Diri](https://drive.google.com/file/d/1tyJU--saXDw5gARV7msA4voQCziVxD4W/view?usp=sharing)
│   ├── Link Game online Pencegahan bullying
│   │   - Link: [Game Pencegahan Bullying](https://mizu-izumi.itch.io/gen-zas)
│   └── Edukasi film Psikoedukasi
│       ├── Sesi 1: Kesakralan dan Filosofi Pernikahan
│       │   - Link: [Sesi 1](https://drive.google.com/file/d/1iRiig-oZncFO20XThcdQ_r588YfsAIXe/view?usp=sharing)
│       ├── Sesi 2: Perawatan dan Pencegahan Pernikahan Muda
│       │   - Link: [Sesi 2](https://drive.google.com/file/d/1avTWpPYSWu6Ldu0pQKhk5ML5TLA6jDaR/view?usp=sharing)
│       ├── Sesi 3: Manajemen Stres dalam Mencegah Pernikahan Muda melalui Budaya Lokal
│       │   - Link: [Sesi 3](https://drive.google.com/file/d/149Esgh0M_Zf8wKg4cQpnpkVJ7MiYoaYz/view?usp=sharing)
│       ├── Sesi 4: Manajemen Beban dalam Pencegahan Pernikahan Muda melalui Budaya Lokal
│       │   - Link: [Sesi 4](https://drive.google.com/file/d/1d1GOjt027X7d7uWOSPVUxUrGAV6-1Dgh/view?usp=sharing)
│       └── Sesi 5: Pemberdayaan melalui Pencegahan Pernikahan Muda melalui Budaya Lokal
│           ├── Eps 1: Teman Sebaya
│           │   - Link: [Eps 1](https://drive.google.com/file/d/11NuU8o2bEJTC1oWelZFxMJC8TJIbkWf_/view?usp=sharing)
│           ├── Eps 2: Orang Tua
│           │   - Link: [Eps 2](https://drive.google.com/file/d/12x7yI1mCnFPqCfaxD1Z14MKiv8nWu6wh/view?usp=sharing)
│           └── Eps 3: Dokter
│               - Link: [Eps 3](https://drive.google.com/file/d/131vair3-jNVIkdIeb_B9jjqHqSrF0wNs/view?usp=sharing)
├── Kolaboratif
│   └── Film animasi
│       ├── Perilaku mencari bantuan kesehatan jiwa formal
│       ├── Perilaku mencari bantuan kesehatan jiwa informal
│       - Link: [Animasi Mencari Bantuan](https://drive.google.com/file/d/1Oy285Boc4pE5mF2uLMd-gepAN6ti5sN-/view?usp=sharing)
│       - Additional Links:
│         - [Additional Link 1](https://drive.google.com/file/d/11NuU8o2eJTCloWeI2FMIC8TjbKwF/view?usp=sharing)
│         - [Additional Link 2](https://drive.google.com/file/d/12x7y1mCnFPoCFaxD)
│         - [Additional Link 3](https://drive.google.com/file/d/13ki5rVNE__H1Fd2UWU3eb_BaTyv6GRVT/edit?usp=sharing&ouid=106103793427421054497&rtpof=true)
│         - [Additional Link 4](https://drive.google.com/file/d/1J9Me3jFdIvwWZFfCzgyoCrHbEk2/view?usp=sharing)

### Description
- **TOKEN PEDIA** is a digital application designed to prevent mental health issues and enhance the well-being of santri in pondok pesantren.
- **Skrining kesehatan jiwa** uses various questionnaires to assess mental health literacy and conditions.
- **Terapeutik** focuses on self-care activities and behavior modification tailored for Pesantren.
- **Modifikasi perilaku** tracks daily activities to form a weekly report.
- **Edukasi/Promosi Kesehatan** provides educational resources, games, and film series.
- **Kolaboratif** includes animated films to encourage formal and informal help-seeking behavior.

*Note: Some links from the PDF image were incomplete or corrupted due to OCR. The "Tabel Modifikasi Perilaku" on page 5 was incomplete with repetitive data; only the header "Hari, Tanggal, Bulan" is included. Additional links from the image are appended under Kolaborasi for completeness.*