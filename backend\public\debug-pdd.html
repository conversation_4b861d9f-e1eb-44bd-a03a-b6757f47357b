<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug PDD Assessment</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Debug PDD Assessment</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Script Loading Status -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Script Loading Status</h2>
                <div id="script-status"></div>
            </div>
            
            <!-- FormConfigs Status -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">FormConfigs Status</h2>
                <div id="config-status"></div>
            </div>
            
            <!-- PDD Configuration -->
            <div class="bg-white p-6 rounded-lg shadow md:col-span-2">
                <h2 class="text-xl font-semibold mb-4">PDD Configuration</h2>
                <div id="pdd-config"></div>
            </div>
            
            <!-- Test Form Engine -->
            <div class="bg-white p-6 rounded-lg shadow md:col-span-2">
                <h2 class="text-xl font-semibold mb-4">Test Form Engine</h2>
                <button id="test-engine" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Test EnhancedFormEngine
                </button>
                <div id="engine-result" class="mt-4"></div>
            </div>
        </div>
    </div>

    <!-- Load Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/form-configs.js"></script>
    <script src="js/enhanced-form-engine.js"></script>

    <script>
        function updateStatus(elementId, status, isSuccess = true) {
            const element = document.getElementById(elementId);
            const color = isSuccess ? 'text-green-600' : 'text-red-600';
            const icon = isSuccess ? '✅' : '❌';
            element.innerHTML += `<div class="${color}">${icon} ${status}</div>`;
        }

        function checkScriptLoading() {
            // Check if utils.js loaded
            if (typeof showToast !== 'undefined') {
                updateStatus('script-status', 'utils.js loaded');
            } else {
                updateStatus('script-status', 'utils.js NOT loaded', false);
            }

            // Check if form-configs.js loaded
            if (typeof FormConfigs !== 'undefined') {
                updateStatus('script-status', 'form-configs.js loaded');
            } else {
                updateStatus('script-status', 'form-configs.js NOT loaded', false);
            }

            // Check if enhanced-form-engine.js loaded
            if (typeof EnhancedFormEngine !== 'undefined') {
                updateStatus('script-status', 'enhanced-form-engine.js loaded');
            } else {
                updateStatus('script-status', 'enhanced-form-engine.js NOT loaded', false);
            }
        }

        function checkFormConfigs() {
            if (typeof FormConfigs === 'undefined') {
                updateStatus('config-status', 'FormConfigs not available', false);
                return;
            }

            updateStatus('config-status', `FormConfigs available with ${Object.keys(FormConfigs).length} configs`);
            updateStatus('config-status', `Available: ${Object.keys(FormConfigs).join(', ')}`);

            if (FormConfigs.PDD) {
                updateStatus('config-status', 'PDD config found');
            } else {
                updateStatus('config-status', 'PDD config NOT found', false);
            }
        }

        function showPDDConfig() {
            if (typeof FormConfigs === 'undefined' || !FormConfigs.PDD) {
                document.getElementById('pdd-config').innerHTML = '<div class="text-red-600">PDD config not available</div>';
                return;
            }

            const config = FormConfigs.PDD;
            document.getElementById('pdd-config').innerHTML = `
                <div class="space-y-2 text-sm">
                    <div><strong>Name:</strong> ${config.name}</div>
                    <div><strong>Category:</strong> ${config.category}</div>
                    <div><strong>Questions:</strong> ${config.questions.length}</div>
                    <div><strong>Answer Options:</strong> ${config.answer_options.length}</div>
                    <div><strong>Scoring Type:</strong> ${config.scoring_rules.type}</div>
                    <div><strong>Max Score:</strong> ${config.scoring_rules.max_score}</div>
                    <div class="mt-4">
                        <strong>First Question:</strong><br>
                        ${config.questions[0]}
                    </div>
                    <div class="mt-4">
                        <strong>Answer Options:</strong><br>
                        ${config.answer_options.map(opt => `${opt.text} (${opt.value})`).join(', ')}
                    </div>
                </div>
            `;
        }

        function testFormEngine() {
            const resultDiv = document.getElementById('engine-result');
            
            try {
                if (typeof EnhancedFormEngine === 'undefined') {
                    throw new Error('EnhancedFormEngine not available');
                }

                if (typeof FormConfigs === 'undefined' || !FormConfigs.PDD) {
                    throw new Error('PDD config not available');
                }

                // Try to create instance
                const engine = new EnhancedFormEngine('PDD');
                resultDiv.innerHTML = '<div class="text-green-600">✅ EnhancedFormEngine created successfully!</div>';
                
                // Test generateOptions
                const options = engine.generateOptions();
                resultDiv.innerHTML += `<div class="text-blue-600">📋 Generated ${options.length} options</div>`;
                resultDiv.innerHTML += `<div class="text-sm text-gray-600">Options: ${options.map(opt => opt.label).join(', ')}</div>`;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="text-red-600">❌ Error: ${error.message}</div>`;
                console.error('Form engine test error:', error);
            }
        }

        // Run checks when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                checkScriptLoading();
                checkFormConfigs();
                showPDDConfig();
                
                document.getElementById('test-engine').addEventListener('click', testFormEngine);
            }, 100);
        });
    </script>
</body>
</html>
