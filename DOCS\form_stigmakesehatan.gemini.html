<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuesioner <PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Custom focus style for radio buttons */
        input[type="radio"]:focus + label {
            outline: 2px solid #3b82f6; /* blue-500 */
            outline-offset: 2px;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div class="container mx-auto max-w-4xl px-4 py-8 sm:py-12">
        
        <!-- Header -->
        <header class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold text-slate-900">Kuesioner Stigma Kesehatan Jiwa</h1>
            <p class="mt-2 text-lg text-slate-600">Berdasarkan Skala Diskriminasi Devaluasi yang Dirasakan oleh Link (1987)</p>
        </header>

        <!-- Instructions -->
        <div class="bg-white p-6 rounded-xl shadow-md mb-8 border border-slate-200">
            <h2 class="text-xl font-semibold mb-3 text-slate-800">Petunjuk Pengisian</h2>
            <p class="text-slate-600 mb-4">Bacalah setiap pernyataan dengan saksama. Pilih jawaban yang paling sesuai dengan pendapat Anda dengan mengeklik salah satu opsi yang tersedia.</p>
            <ul class="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
                <li class="bg-blue-50 p-3 rounded-lg"><span class="font-bold text-blue-700">SS</span>: Sangat Setuju</li>
                <li class="bg-green-50 p-3 rounded-lg"><span class="font-bold text-green-700">S</span>: Setuju</li>
                <li class="bg-yellow-50 p-3 rounded-lg"><span class="font-bold text-yellow-700">TS</span>: Tidak Setuju</li>
                <li class="bg-red-50 p-3 rounded-lg"><span class="font-bold text-red-700">STS</span>: Sangat Tidak Setuju</li>
            </ul>
        </div>

        <!-- Questionnaire Form -->
        <form id="stigma-form" class="space-y-6">
            <!-- Questions will be injected here by JavaScript -->
        </form>
        
        <!-- Action Button -->
        <div class="mt-10 text-center">
            <button id="calculate-btn" type="button" class="bg-blue-600 text-white font-bold py-3 px-8 rounded-lg shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-300 transform hover:scale-105 disabled:bg-slate-400 disabled:cursor-not-allowed disabled:scale-100">
                Selesai & Lihat Skor
            </button>
        </div>

        <!-- Error Message -->
        <div id="error-message" class="hidden mt-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert">
            <p class="font-bold">Gagal Menghitung</p>
            <p>Harap jawab semua 12 pertanyaan sebelum melihat skor.</p>
        </div>

        <!-- Result Section -->
        <div id="result-section" class="hidden mt-10 bg-white p-8 rounded-xl shadow-lg border border-slate-200 text-center transition-all duration-500 ease-in-out transform scale-95 opacity-0">
            <h2 class="text-2xl font-bold text-slate-900 mb-4">Hasil Kuesioner Anda</h2>
            <div class="bg-blue-100 rounded-full w-40 h-40 mx-auto flex items-center justify-center mb-4 border-4 border-blue-200">
                <div>
                    <p id="score-display" class="text-5xl font-bold text-blue-700">0</p>
                    <p class="text-slate-600">dari 36</p>
                </div>
            </div>
            <p class="text-slate-600 max-w-md mx-auto">Skor ini merefleksikan persepsi Anda terhadap stigma publik tentang kesehatan jiwa. Skor yang lebih tinggi menunjukkan persepsi stigma yang lebih tinggi.</p>
            <button id="reset-btn" type="button" class="mt-6 bg-slate-200 text-slate-800 font-semibold py-2 px-6 rounded-lg hover:bg-slate-300 focus:outline-none focus:ring-4 focus:ring-slate-300 transition-colors">
                Isi Ulang Kuesioner
            </button>
        </div>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- DATA ---
            const questions = [
                "Kebanyakan orang berpikir bahwa seseorang yang memiliki riwayat gangguan jiwa tidak sepenuhnya cerdas.",
                "Kebanyakan orang akan menerima seseorang yang pernah dirawat karena gangguan jiwa sebagai teman dekat.",
                "Kebanyakan orang percaya bahwa orang dengan gangguan jiwa tidak bisa diandalkan dalam pekerjaan.",
                "Saya akan merasa malu jika orang-orang tahu bahwa anggota keluarga saya menderita gangguan jiwa.",
                "Kebanyakan pengusaha akan menolak mempekerjakan seseorang yang pernah dirawat karena gangguan jiwa.",
                "Kebanyakan orang akan dengan sengaja menghindari berbicara dengan seseorang yang memiliki riwayat gangguan jiwa.",
                "Kebanyakan orang tidak akan menganggap serius pendapat dari seseorang yang pernah memiliki masalah kejiwaan.",
                "Kebanyakan orang percaya bahwa mencari bantuan untuk masalah kejiwaan adalah tanda kelemahan pribadi.",
                "Begitu seseorang memiliki masalah kejiwaan, mereka cenderung tidak akan pernah pulih sepenuhnya.",
                "Kebanyakan orang di komunitas saya akan memperlakukan seseorang yang baru keluar dari rumah sakit jiwa secara berbeda.",
                "Saya akan ragu untuk menjalin hubungan romantis dengan seseorang yang memiliki riwayat gangguan jiwa.",
                "Kebanyakan orang berpikir bahwa orang dengan gangguan jiwa berbahaya."
            ];

            const options = [
                { label: 'SS', value: 3, color: 'blue' },
                { label: 'S', value: 2, color: 'green' },
                { label: 'TS', value: 1, color: 'yellow' },
                { label: 'STS', value: 0, color: 'red' }
            ];

            // --- REFERENCES TO DOM ELEMENTS ---
            const form = document.getElementById('stigma-form');
            const calculateBtn = document.getElementById('calculate-btn');
            const resultSection = document.getElementById('result-section');
            const scoreDisplay = document.getElementById('score-display');
            const errorMessage = document.getElementById('error-message');
            const resetBtn = document.getElementById('reset-btn');

            // --- FUNCTIONS ---

            /**
             * Generates and injects question HTML into the form
             */
            function renderQuestions() {
                let questionHTML = '';
                questions.forEach((q, index) => {
                    questionHTML += `
                        <div class="bg-white p-5 rounded-xl shadow-sm border border-slate-200 transition-all duration-300 hover:shadow-md hover:border-blue-300">
                            <p class="text-base sm:text-lg font-medium text-slate-800 mb-4">${index + 1}. ${q}</p>
                            <fieldset>
                                <legend class="sr-only">Jawaban untuk pertanyaan ${index + 1}</legend>
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-2 sm:gap-4">
                                    ${options.map(opt => `
                                        <div class="flex-1">
                                            <input type="radio" id="q${index}_${opt.label}" name="question_${index}" value="${opt.value}" class="sr-only">
                                            <label for="q${index}_${opt.label}" class="w-full text-center block py-2 px-4 rounded-md border-2 border-slate-200 cursor-pointer transition-all duration-200 hover:bg-slate-100 peer-checked:bg-${opt.color}-500 peer-checked:text-white peer-checked:border-${opt.color}-500 peer-checked:font-bold">
                                                ${opt.label}
                                            </label>
                                        </div>
                                    `).join('')}
                                </div>
                            </fieldset>
                        </div>
                    `;
                });
                form.innerHTML = questionHTML;
                
                // This is a trick to make Tailwind's JIT compiler recognize the dynamic classes
                // We don't actually use these, but their presence ensures the styles are generated.
                const hiddenDynamicClasses = 'bg-blue-500 text-white border-blue-500 bg-green-500 text-white border-green-500 bg-yellow-500 text-white border-yellow-500 bg-red-500 text-white border-red-500';
            }

            /**
             * Calculates the total score from selected answers
             */
            function calculateScore() {
                const checkedRadios = form.querySelectorAll('input[type="radio"]:checked');

                // Validation: Ensure all questions are answered
                if (checkedRadios.length < questions.length) {
                    errorMessage.classList.remove('hidden');
                    resultSection.classList.add('hidden');
                    resultSection.classList.add('opacity-0', 'scale-95');
                    errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    return;
                }

                // Hide error if all questions are answered
                errorMessage.classList.add('hidden');

                // Calculate total score
                let totalScore = 0;
                checkedRadios.forEach(radio => {
                    totalScore += parseInt(radio.value, 10);
                });

                // Display the result
                scoreDisplay.textContent = totalScore;
                resultSection.classList.remove('hidden');
                setTimeout(() => {
                    resultSection.classList.remove('opacity-0', 'scale-95');
                }, 10); // small delay to allow transition
                
                resultSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            
            /**
             * Resets the entire form and results
             */
            function resetForm() {
                form.reset();
                resultSection.classList.add('hidden');
                resultSection.classList.add('opacity-0', 'scale-95');
                errorMessage.classList.add('hidden');
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }


            // --- EVENT LISTENERS ---
            calculateBtn.addEventListener('click', calculateScore);
            resetBtn.addEventListener('click', resetForm);
            
            // Initial render
            renderQuestions();
        });
    </script>
</body>
</html>
