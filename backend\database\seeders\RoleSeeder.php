<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin Role
        Role::updateOrCreate(
            ['name' => 'admin'],
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Administrator sistem dengan akses penuh',
                'permissions' => [
                    'manage_users',
                    'manage_roles',
                    'manage_forms',
                    'view_all_assessments',
                    'manage_assessments',
                    'view_reports',
                    'manage_system',
                    'export_data'
                ],
                'is_active' => true
            ]
        );

        // Guru Role
        Role::updateOrCreate(
            ['name' => 'guru'],
            [
                'name' => 'guru',
                'display_name' => 'Guru',
                'description' => 'Guru yang dapat mengelola siswa dan melihat hasil assessment',
                'permissions' => [
                    'view_student_assessments',
                    'manage_students',
                    'view_class_reports',
                    'assign_assessments',
                    'view_student_profiles'
                ],
                'is_active' => true
            ]
        );

        // Orangtua Role
        Role::updateOrCreate(
            ['name' => 'orangtua'],
            [
                'name' => 'orangtua',
                'display_name' => 'Orang Tua',
                'description' => 'Orang tua yang dapat melihat hasil assessment anak',
                'permissions' => [
                    'view_child_assessments',
                    'view_child_profile',
                    'receive_notifications'
                ],
                'is_active' => true
            ]
        );

        // Siswa Role
        Role::updateOrCreate(
            ['name' => 'siswa'],
            [
                'name' => 'siswa',
                'display_name' => 'Siswa',
                'description' => 'Siswa yang dapat mengikuti assessment',
                'permissions' => [
                    'take_assessments',
                    'view_own_results',
                    'update_profile'
                ],
                'is_active' => true
            ]
        );
    }
}
