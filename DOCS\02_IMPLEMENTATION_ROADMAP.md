# TOKEN PEDIA - Implementation Roadmap
## Comprehensive Mental Health Platform for Santri

### Phase 1: Core Assessment System (Week 1-2)
#### 1.1 Database Schema Enhancement
- Extend `form_templates` table for 7 assessment types
- Add `therapeutic_modules` table
- Add `educational_content` table
- Add `behavior_tracking` table

#### 1.2 Assessment Forms Implementation
**Priority Order:**
1. **SRQ-20** ✅ (Already implemented)
2. **DASS-42** - Depression, Anxiety, Stress (42 questions, Likert 0-3)
3. **GSE** - General Self-Efficacy (10 questions, Likert 1-4)
4. **MHKQ** - Mental Health Knowledge (True/False questions)
5. **MSCS** - Mindful Self-Care Scale (Likert 1-5)
6. **PDD** - Perceived Devaluation Discrimination (Likert 1-4)

#### 1.3 Scoring Algorithms
```php
// New scoring types to implement
- 'dass42_subscales' => Depression, Anxiety, Stress subscales
- 'gse_sum' => Self-efficacy total score
- 'knowledge_percentage' => MHKQ correct answers percentage
- 'mscs_domains' => 6 self-care domains
- 'pdd_discrimination' => Perceived discrimination level
```

### Phase 2: Therapeutic Modules (Week 3-4)
#### 2.1 Self-Care Module
- Digital self-care guide integration
- Daily activity tracking
- Weekly behavior modification reports
- Progress visualization

#### 2.2 Behavior Modification System
```sql
CREATE TABLE behavior_tracking (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    date DATE,
    activities JSON, -- Daily activities
    mood_rating INT,
    notes TEXT,
    weekly_report_id BIGINT
);
```

### Phase 3: Educational Content (Week 5-6)
#### 3.1 E-Learning Modules
- Psychoeducation content management
- Video integration system
- Interactive learning paths
- Progress tracking

#### 3.2 Content Structure
```json
{
  "modules": [
    {
      "id": "psikoedukasi",
      "title": "E-Modul Psikoedukasi Kesehatan Jiwa",
      "type": "document",
      "url": "drive.google.com/file/d/1J9Me3jFdIvwWZFfCzgyoCrHbEk2"
    },
    {
      "id": "self_care_digital",
      "title": "Modul Digital Perawatan Diri",
      "type": "interactive",
      "url": "drive.google.com/file/d/1tyJU--saXDw5gARV7msA4voQCziVxD4W"
    }
  ]
}
```

### Phase 4: Gamification & Interactive Content (Week 7-8)
#### 4.1 Anti-Bullying Game Integration
- Embed game: https://mizu-izumi.itch.io/gen-zas
- Score tracking
- Achievement system

#### 4.2 Video Education Series
**Marriage Prevention Education (5 Sessions):**
1. Kesakralan dan Filosofi Pernikahan
2. Perawatan dan Pencegahan Pernikahan Muda
3. Manajemen Stres melalui Budaya Lokal
4. Manajemen Beban melalui Budaya Lokal
5. Pemberdayaan (3 Episodes: Teman Sebaya, Orang Tua, Dokter)

### Phase 5: Collaborative Features (Week 9-10)
#### 5.1 Help-Seeking Behavior
- Formal help-seeking guidance
- Informal support network
- Animated educational content
- Peer support system

#### 5.2 Professional Integration
- Counselor dashboard
- Referral system
- Crisis intervention protocols

### Phase 6: Mobile Optimization (Week 11-12)
#### 6.1 Progressive Web App (PWA)
- Offline capability
- Push notifications
- App-like experience
- WebView optimization for Android

#### 6.2 Mobile-Specific Features
- Touch-optimized UI
- Swipe gestures
- Mobile notifications
- Camera integration (QR codes)

## Technical Implementation Details

### Database Migrations
```php
// Add new assessment types
Schema::table('form_templates', function (Blueprint $table) {
    $table->json('therapeutic_content')->nullable();
    $table->json('educational_links')->nullable();
    $table->boolean('has_video_content')->default(false);
});

// Therapeutic modules
Schema::create('therapeutic_modules', function (Blueprint $table) {
    $table->id();
    $table->string('code')->unique();
    $table->string('title');
    $table->text('description');
    $table->json('content_links');
    $table->json('activities');
    $table->timestamps();
});
```

### API Endpoints Extension
```php
// New routes for TOKEN PEDIA
Route::group(['prefix' => 'api'], function () {
    // Assessments (extended)
    Route::get('/assessments/types', 'AssessmentController@getTypes');
    Route::post('/assessments/{type}/submit', 'AssessmentController@submit');
    
    // Therapeutic
    Route::get('/therapeutic/modules', 'TherapeuticController@getModules');
    Route::post('/therapeutic/behavior-tracking', 'TherapeuticController@trackBehavior');
    
    // Educational
    Route::get('/education/content', 'EducationController@getContent');
    Route::post('/education/progress', 'EducationController@trackProgress');
    
    // Collaborative
    Route::get('/support/resources', 'SupportController@getResources');
    Route::post('/support/help-request', 'SupportController@requestHelp');
});
```

### Frontend Components
```javascript
// New components to develop
components/
├── assessments/
│   ├── DASS42Form.js
│   ├── GSEForm.js
│   ├── MHKQForm.js
│   ├── MSCSForm.js
│   └── PDDForm.js
├── therapeutic/
│   ├── SelfCareModule.js
│   ├── BehaviorTracker.js
│   └── WeeklyReport.js
├── education/
│   ├── VideoPlayer.js
│   ├── ModuleViewer.js
│   └── ProgressTracker.js
└── collaborative/
    ├── HelpSeeking.js
    ├── PeerSupport.js
    └── CrisisSupport.js
```

## Success Metrics
- ✅ 6 assessment types fully functional
- ✅ Therapeutic modules integrated
- ✅ Educational content accessible
- ✅ Mobile-optimized experience
- ✅ 95%+ uptime
- ✅ <3s page load time
- ✅ Accessibility compliance

## Risk Mitigation
1. **Content Integration**: Backup plans for external links
2. **Mobile Performance**: Progressive loading strategies
3. **Data Privacy**: Enhanced encryption for sensitive data
4. **Scalability**: Load balancing for high user volume

## Timeline Summary
- **Week 1-2**: Core assessments
- **Week 3-4**: Therapeutic features
- **Week 5-6**: Educational content
- **Week 7-8**: Gamification
- **Week 9-10**: Collaborative features
- **Week 11-12**: Mobile optimization

**Total Development Time**: 12 weeks
**Team Required**: 2-3 developers, 1 UI/UX designer, 1 mental health consultant
