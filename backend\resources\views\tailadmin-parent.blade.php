@extends('tailadmin-base')

@section('title', 'Dashboard Orang Tua - SantriMental')
@section('dashboard-subtitle', 'Dashboard Orang Tua')
@section('page-title', 'Dashboard Orang Tua')
@section('page-description', 'Pantau kesehatan mental anak <PERSON>a dengan penuh kasih sayang')
@section('user-initials', 'OT')
@section('user-name', 'Orang Tua Demo')
@section('user-role', 'Parent')

@section('logo-icon')
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
</svg>
@endsection

@section('navigation')
<div class="mb-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6">Menu Utama</p>

    <a href="#" class="tailadmin-nav-item active">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="font-medium">Dashboard</span>
        <div class="ml-auto">
            <div class="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
        </div>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
        <span class="font-medium">Anak Saya</span>
        <div class="ml-auto">
            <span class="tailadmin-badge primary text-xs">2</span>
        </div>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <span class="font-medium">Laporan</span>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
        </svg>
        <span class="font-medium">Konsultasi</span>
        <div class="ml-auto">
            <span class="tailadmin-badge warning text-xs">Soon</span>
        </div>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <span class="font-medium">Panduan Orang Tua</span>
    </a>
</div>

<div class="border-t border-gray-200 dark:border-dark-300 pt-4 mt-6">
    <p class="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase tracking-wider mb-3 px-6">Akun</p>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        <span class="font-medium">Profil Keluarga</span>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        <span class="font-medium">Pengaturan</span>
    </a>

    <a href="#" class="tailadmin-nav-item">
        <svg class="tailadmin-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">Bantuan</span>
    </a>
</div>
@endsection

@section('header-actions')
<!-- Family Status -->
<div class="hidden lg:flex items-center space-x-2 px-3 py-2 bg-warning-100 dark:bg-warning-900 rounded-lg border border-warning-200 dark:border-warning-800">
    <div class="w-2 h-2 bg-warning-500 rounded-full animate-pulse"></div>
    <span class="text-warning-600 dark:text-warning-400 text-sm font-medium">Keluarga Terhubung</span>
</div>

<!-- Add Child -->
<button class="tailadmin-btn tailadmin-btn-primary">
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Tambah Anak
</button>
@endsection

@section('breadcrumb')
<a href="/" class="tailadmin-text-secondary hover:text-primary-500 transition-colors">Beranda</a>
<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
</svg>
<span class="tailadmin-text-primary font-medium">Dashboard Orang Tua</span>
@endsection

@section('content')
<!-- Welcome Banner -->
<div class="tailadmin-card mb-8 tailadmin-fade-in-up">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-warning-500 to-warning-600 rounded-2xl flex items-center justify-center mr-6 shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </div>
            <div>
                <h2 class="text-2xl font-bold tailadmin-text-primary mb-2">Dashboard Monitoring Anak</h2>
                <p class="tailadmin-text-secondary text-lg">Pantau kesehatan mental anak Anda dengan penuh kasih sayang</p>
                <div class="flex items-center mt-3 space-x-4">
                    <div class="flex items-center text-sm text-success-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span>Privasi Terjaga</span>
                    </div>
                    <div class="flex items-center text-sm text-primary-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <span>Dukungan Keluarga</span>
                    </div>
                    <div class="flex items-center text-sm text-secondary-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span>Konsultasi Tersedia</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="hidden md:block">
            <div class="text-right">
                <p class="tailadmin-text-secondary text-sm">Anak Terdaftar</p>
                <p class="tailadmin-text-primary font-semibold text-lg">2 Anak</p>
                <p class="tailadmin-text-secondary text-sm">Aktif Monitoring</p>
            </div>
        </div>
    </div>
</div>

<!-- Parent Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.1s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon primary">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                Aktif
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Jumlah Anak</p>
            <p class="tailadmin-stats-value">2</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Anak terdaftar</p>
        </div>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.2s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon success">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +8
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Skrining Selesai</p>
            <p class="tailadmin-stats-value">15</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Total tes</p>
        </div>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.3s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon success">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-success-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Baik
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Status Kesehatan</p>
            <p class="text-xl font-bold text-success-500 mb-1">Normal</p>
            <p class="text-xs tailadmin-text-secondary">Kondisi stabil</p>
        </div>
    </div>

    <div class="tailadmin-stats-card tailadmin-fade-in-up" style="animation-delay: 0.4s;">
        <div class="flex items-center justify-between mb-4">
            <div class="tailadmin-stats-icon warning">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10a2 2 0 002 2h4a2 2 0 002-2V11m-6 0h6"></path>
                </svg>
            </div>
            <div class="flex items-center text-xs text-primary-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Baru
            </div>
        </div>
        <div>
            <p class="tailadmin-stats-label">Tes Terakhir</p>
            <p class="tailadmin-stats-value">3</p>
            <p class="text-xs tailadmin-text-secondary mt-1">Hari yang lalu</p>
        </div>
    </div>
</div>

<!-- Children Cards -->
<div class="mb-8">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-2xl font-bold tailadmin-text-primary">Anak-anak Saya</h3>
        <button class="tailadmin-btn tailadmin-btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Tambah Anak
        </button>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Child Card 1 -->
        <div class="tailadmin-card tailadmin-fade-in-up" style="animation-delay: 0.5s;">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                    <span class="text-xs text-success-500 font-medium">Aktif</span>
                </div>
            </div>
            <div>
                <h4 class="text-lg font-semibold tailadmin-text-primary mb-1">Ahmad Fauzi</h4>
                <p class="tailadmin-text-secondary text-sm mb-3">Kelas 10A • SMA Negeri 1</p>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="tailadmin-text-secondary">Status Mental:</span>
                        <span class="text-success-500 font-medium">Normal</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="tailadmin-text-secondary">Tes Terakhir:</span>
                        <span class="tailadmin-text-primary">2 hari lalu</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="tailadmin-text-secondary">Skor SRQ-20:</span>
                        <span class="tailadmin-text-primary font-medium">4/20</span>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-dark-300">
                    <div class="flex space-x-2">
                        <button class="tailadmin-btn tailadmin-btn-outline text-xs px-3 py-2 flex-1">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Detail
                        </button>
                        <button class="tailadmin-btn tailadmin-btn-outline text-xs px-3 py-2 flex-1">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Chat
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Child Card 2 -->
        <div class="tailadmin-card tailadmin-fade-in-up" style="animation-delay: 0.6s;">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-secondary-100 dark:bg-secondary-900 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-warning-500 rounded-full animate-pulse"></div>
                    <span class="text-xs text-warning-500 font-medium">Perhatian</span>
                </div>
            </div>
            <div>
                <h4 class="text-lg font-semibold tailadmin-text-primary mb-1">Siti Aisyah</h4>
                <p class="tailadmin-text-secondary text-sm mb-3">Kelas 11B • SMA Negeri 1</p>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="tailadmin-text-secondary">Status Mental:</span>
                        <span class="text-warning-500 font-medium">Perlu Perhatian</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="tailadmin-text-secondary">Tes Terakhir:</span>
                        <span class="tailadmin-text-primary">1 hari lalu</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="tailadmin-text-secondary">Skor SRQ-20:</span>
                        <span class="text-warning-500 font-medium">12/20</span>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-dark-300">
                    <div class="flex space-x-2">
                        <button class="tailadmin-btn tailadmin-btn-outline text-xs px-3 py-2 flex-1">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Detail
                        </button>
                        <button class="tailadmin-btn tailadmin-btn-warning text-xs px-3 py-2 flex-1">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            Konsultasi
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Child Card -->
        <div class="tailadmin-card border-2 border-dashed border-gray-300 dark:border-dark-400 hover:border-primary-500 transition-all duration-300 cursor-pointer tailadmin-fade-in-up" style="animation-delay: 0.7s;">
            <div class="text-center py-8">
                <div class="w-16 h-16 bg-gray-100 dark:bg-dark-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 tailadmin-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <h4 class="text-lg font-semibold tailadmin-text-primary mb-2">Tambah Anak</h4>
                <p class="tailadmin-text-secondary text-sm mb-4">Daftarkan anak baru untuk monitoring kesehatan mental</p>
                <button class="tailadmin-btn tailadmin-btn-primary w-full">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Daftar Sekarang
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Show welcome notification
    setTimeout(() => {
        showToast('Selamat datang di Dashboard Orang Tua! Monitor kesehatan mental anak Anda dengan mudah.', 'success');
    }, 1000);
});
</script>
@endpush
