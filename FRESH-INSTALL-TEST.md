# 🧪 SantriMental Fresh Installation Testing Guide

## 🎯 **Testing Objective**

Memverifikasi bahwa modul-modul SantriMental dapat diinstal dengan sukses pada fresh Laravel installation tanpa dependency pada instalasi sebelumnya.

## 📋 **Pre-Test Checklist**

### **Requirements**
- ✅ PHP >= 8.1
- ✅ Composer
- ✅ MySQL/PostgreSQL
- ✅ Node.js >= 16 (optional, untuk asset compilation)

### **Test Environment**
- ✅ Fresh directory (bukan existing Laravel)
- ✅ Clean database
- ✅ No existing SantriMental installation

## 🚀 **Testing Steps**

### **Step 1: Create Fresh Laravel Project**

```bash
# Navigate to test directory
cd C:\laragon\www\

# Create fresh Laravel project
composer create-project laravel/laravel santrimental-test
cd santrimental-test

# Verify Laravel installation
php artisan --version
```

**Expected Result**: Laravel installation successful, artisan command works

### **Step 2: Extract SantriMental Modules**

```bash
# Copy santrimental-modules folder to project root
# Your directory structure should look like:
# santrimental-test/
# ├── app/
# ├── bootstrap/
# ├── config/
# ├── santrimental-modules/  ← This folder
# ├── database/
# ├── public/
# ├── resources/
# ├── routes/
# └── ...
```

**Expected Result**: santrimental-modules folder exists in project root

### **Step 3: Run SantriMental Installer**

```bash
# Run the installer
santrimental-modules\install.bat

# Or manually if batch fails:
# Copy views
copy santrimental-modules\resources\views\*.blade.php resources\views\

# Copy assets
copy santrimental-modules\public\js\*.js public\js\
copy santrimental-modules\public\css\*.css public\css\

# Copy backend files
copy santrimental-modules\app\Http\Controllers\Api\*.php app\Http\Controllers\Api\
copy santrimental-modules\app\Models\*.php app\Models\
copy santrimental-modules\database\migrations\*.php database\migrations\
copy santrimental-modules\database\seeders\*.php database\seeders\
copy santrimental-modules\routes\*.php routes\
copy santrimental-modules\app\Http\Middleware\*.php app\Http\Middleware\
```

**Expected Result**: All files copied successfully, no errors

### **Step 4: Configure Environment**

```bash
# Update .env file
APP_NAME=SantriMental
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=santrimental_test
DB_USERNAME=root
DB_PASSWORD=

# Create database
mysql -u root -p
CREATE DATABASE santrimental_test;
exit
```

**Expected Result**: Database created, .env configured

### **Step 5: Install Dependencies**

```bash
# Install PHP dependencies
composer install

# Generate application key
php artisan key:generate

# Clear any cache
php artisan config:clear
php artisan cache:clear
```

**Expected Result**: Dependencies installed, key generated

### **Step 6: Run Database Migrations**

```bash
# Run migrations
php artisan migrate

# Check migration status
php artisan migrate:status
```

**Expected Result**: All migrations run successfully, no errors

### **Step 7: Seed Sample Data**

```bash
# Run seeders
php artisan db:seed

# Or run specific seeders
php artisan db:seed --class=RoleSeeder
php artisan db:seed --class=UserSeeder
php artisan db:seed --class=FormTemplateSeeder
```

**Expected Result**: Sample data created, no errors

### **Step 8: Start Application**

```bash
# Start development server
php artisan serve

# Or if using Laragon
# Access via http://localhost/santrimental-test/public
```

**Expected Result**: Server starts successfully

### **Step 9: Test Application Access**

#### **Test URLs:**
1. **Home Page**: `http://localhost:8000`
   - ✅ Modern landing page loads
   - ✅ CSS styling applied correctly
   - ✅ JavaScript components work
   - ✅ No console errors

2. **Student Dashboard**: `http://localhost:8000/dashboard`
   - ✅ Dashboard loads with mock data
   - ✅ Charts render correctly
   - ✅ Navigation works
   - ✅ Responsive design

3. **Admin Dashboard**: `http://localhost:8000/admin/dashboard`
   - ✅ Admin interface loads
   - ✅ Statistics display correctly
   - ✅ Modern UI components work

4. **Teacher Dashboard**: `http://localhost:8000/guru/dashboard`
   - ✅ Teacher interface loads
   - ✅ Student monitoring features work

5. **Parent Dashboard**: `http://localhost:8000/orangtua/dashboard`
   - ✅ Parent interface loads
   - ✅ Child monitoring features work

6. **Assessments**: `http://localhost:8000/assessments`
   - ✅ Assessment selection page loads
   - ✅ Form system works

7. **History**: `http://localhost:8000/history`
   - ✅ History page loads
   - ✅ Data display works

### **Step 10: Test Authentication (Optional)**

```bash
# Test login with seeded users
# Admin: <EMAIL> / password
# Teacher: <EMAIL> / password
# Student: <EMAIL> / password
# Parent: <EMAIL> / password
```

**Expected Result**: Login works, role-based access functions

## ✅ **Success Criteria**

### **Installation Success**
- ✅ All files copied without errors
- ✅ No missing dependencies
- ✅ Database migrations run successfully
- ✅ Seeders execute without errors
- ✅ Application starts without errors

### **Functionality Success**
- ✅ All URLs accessible
- ✅ Modern UI renders correctly
- ✅ JavaScript components work
- ✅ CSS styling applied
- ✅ No browser console errors
- ✅ Database queries work
- ✅ Mock data displays correctly

### **Performance Success**
- ✅ Pages load quickly (< 2 seconds)
- ✅ Smooth animations
- ✅ Responsive design works
- ✅ No memory leaks

## 🐛 **Common Issues & Solutions**

### **Issue 1: File Copy Errors**
```bash
# Solution: Check file permissions
# Run as administrator if needed
# Verify source files exist
```

### **Issue 2: Migration Errors**
```bash
# Solution: Check database connection
# Verify .env configuration
# Ensure database exists
php artisan migrate:fresh
```

### **Issue 3: Asset Loading Issues**
```bash
# Solution: Check file paths
# Verify public folder structure
# Clear browser cache
```

### **Issue 4: JavaScript Errors**
```bash
# Solution: Check browser console
# Verify all JS files copied
# Check for syntax errors
```

### **Issue 5: CSS Not Loading**
```bash
# Solution: Check CSS file path
# Verify modern-dashboard.css exists
# Check for CSS syntax errors
```

## 📊 **Test Results Template**

```
=== SantriMental Fresh Installation Test Results ===

Date: ___________
Tester: ___________
Environment: ___________

Installation Steps:
[ ] Step 1: Fresh Laravel - PASS/FAIL
[ ] Step 2: Extract Modules - PASS/FAIL  
[ ] Step 3: Run Installer - PASS/FAIL
[ ] Step 4: Configure Environment - PASS/FAIL
[ ] Step 5: Install Dependencies - PASS/FAIL
[ ] Step 6: Run Migrations - PASS/FAIL
[ ] Step 7: Seed Data - PASS/FAIL
[ ] Step 8: Start Application - PASS/FAIL

URL Testing:
[ ] Home Page - PASS/FAIL
[ ] Student Dashboard - PASS/FAIL
[ ] Admin Dashboard - PASS/FAIL
[ ] Teacher Dashboard - PASS/FAIL
[ ] Parent Dashboard - PASS/FAIL
[ ] Assessments - PASS/FAIL
[ ] History - PASS/FAIL

Overall Result: PASS/FAIL

Notes:
_________________________________
_________________________________
_________________________________
```

## 🎯 **Next Steps After Successful Test**

1. **Package for Distribution**
   - Create ZIP file of santrimental-modules
   - Include installation guide
   - Add troubleshooting documentation

2. **Create Automated Installer**
   - Improve batch script
   - Add error handling
   - Create cross-platform version

3. **Documentation**
   - Update installation guide
   - Create video tutorial
   - Add FAQ section

4. **Quality Assurance**
   - Test on different environments
   - Verify cross-browser compatibility
   - Performance testing

---

**Happy Testing! 🚀**

Jika semua test berhasil, berarti modul SantriMental siap untuk distribusi dan instalasi fresh!
