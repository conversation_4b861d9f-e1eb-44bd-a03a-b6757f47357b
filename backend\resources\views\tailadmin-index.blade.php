@extends('tailadmin-base', ['hideSidebar' => true, 'hideHeader' => true])

@section('title', 'SantriMental - Mental Health Assessment Platform')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-slate-900 dark:to-slate-800">
    <div class="max-w-4xl w-full mx-4">
        
        <!-- Header Section -->
        <div class="text-center mb-12 tailadmin-fade-in-up">
            <div class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </div>
            <h1 class="text-5xl font-bold tailadmin-text-primary mb-4">SantriMental</h1>
            <p class="text-xl tailadmin-text-secondary mb-6">Platform Asesmen Kesehatan Mental Terpadu</p>
            <div class="flex items-center justify-center space-x-2">
                <div class="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-success-500 font-medium">System Online & Ready</span>
            </div>
        </div>
        
        <!-- Dashboard Selection -->
        <div class="tailadmin-card mb-8" style="animation-delay: 0.2s;">
            <div class="tailadmin-card-header text-center">
                <h2 class="tailadmin-card-title text-2xl">Pilih Dashboard</h2>
                <p class="tailadmin-text-secondary mt-2">Akses dashboard sesuai dengan peran Anda</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Student Dashboard -->
                <a href="/dashboard" class="group block">
                    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 border-2 border-transparent group-hover:border-primary-500">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold tailadmin-text-primary text-lg">Dashboard Siswa</h3>
                                <p class="tailadmin-text-secondary text-sm">Monitor kesehatan mental pribadi</p>
                            </div>
                            <svg class="w-5 h-5 tailadmin-text-secondary group-hover:text-primary-500 group-hover:translate-x-1 transition-all" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-4 text-sm">
                            <span class="flex items-center text-success-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Self Assessment
                            </span>
                            <span class="flex items-center text-primary-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Progress Tracking
                            </span>
                        </div>
                    </div>
                </a>

                <!-- Parent Dashboard -->
                <a href="/orangtua/dashboard" class="group block">
                    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 border-2 border-transparent group-hover:border-warning-500">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <svg class="w-6 h-6 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold tailadmin-text-primary text-lg">Dashboard Orang Tua</h3>
                                <p class="tailadmin-text-secondary text-sm">Monitor kesehatan mental anak</p>
                            </div>
                            <svg class="w-5 h-5 tailadmin-text-secondary group-hover:text-warning-500 group-hover:translate-x-1 transition-all" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-4 text-sm">
                            <span class="flex items-center text-success-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                Family Monitoring
                            </span>
                            <span class="flex items-center text-warning-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                Consultation
                            </span>
                        </div>
                    </div>
                </a>

                <!-- Teacher Dashboard -->
                <a href="/guru/dashboard" class="group block">
                    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 border-2 border-transparent group-hover:border-success-500">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <svg class="w-6 h-6 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold tailadmin-text-primary text-lg">Dashboard Guru</h3>
                                <p class="tailadmin-text-secondary text-sm">Monitor siswa di kelas</p>
                            </div>
                            <svg class="w-5 h-5 tailadmin-text-secondary group-hover:text-success-500 group-hover:translate-x-1 transition-all" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-4 text-sm">
                            <span class="flex items-center text-success-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                Class Management
                            </span>
                            <span class="flex items-center text-primary-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Early Detection
                            </span>
                        </div>
                    </div>
                </a>

                <!-- Admin Dashboard -->
                <a href="/admin/dashboard" class="group block">
                    <div class="tailadmin-card hover:shadow-dropdown transition-all duration-300 border-2 border-transparent group-hover:border-secondary-500">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-secondary-100 dark:bg-secondary-900 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <svg class="w-6 h-6 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold tailadmin-text-primary text-lg">Dashboard Admin</h3>
                                <p class="tailadmin-text-secondary text-sm">Kelola sistem secara menyeluruh</p>
                            </div>
                            <svg class="w-5 h-5 tailadmin-text-secondary group-hover:text-secondary-500 group-hover:translate-x-1 transition-all" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-4 text-sm">
                            <span class="flex items-center text-success-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                System Analytics
                            </span>
                            <span class="flex items-center text-secondary-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                User Management
                            </span>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Quick Access Features -->
        <div class="tailadmin-card mb-8" style="animation-delay: 0.4s;">
            <div class="tailadmin-card-header text-center">
                <h3 class="tailadmin-card-title">Fitur Utama</h3>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/assessments" class="text-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                    <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                    <h4 class="font-medium tailadmin-text-primary text-sm">Assessment</h4>
                    <p class="tailadmin-text-secondary text-xs">SRQ-20 Test</p>
                </a>
                
                <a href="/history" class="text-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                    <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h4 class="font-medium tailadmin-text-primary text-sm">History</h4>
                    <p class="tailadmin-text-secondary text-xs">Test Records</p>
                </a>
                
                <div class="text-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors cursor-pointer">
                    <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h4 class="font-medium tailadmin-text-primary text-sm">Consultation</h4>
                    <p class="tailadmin-text-secondary text-xs">Coming Soon</p>
                </div>
                
                <div class="text-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors cursor-pointer">
                    <div class="w-12 h-12 bg-secondary-100 dark:bg-secondary-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h4 class="font-medium tailadmin-text-primary text-sm">Resources</h4>
                    <p class="tailadmin-text-secondary text-xs">Mental Health</p>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="tailadmin-card text-center" style="animation-delay: 0.6s;">
            <div class="flex items-center justify-center space-x-6">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-success-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-success-500">TailAdmin Framework</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-primary-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-primary-500">Responsive Design</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-warning-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-warning-500">Dark Mode Ready</span>
                </div>
            </div>
            <p class="tailadmin-text-secondary text-sm mt-4">
                🚀 Powered by TailAdmin Design System - Professional Admin Dashboard Template
            </p>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Add staggered animation to cards
    const cards = document.querySelectorAll('.tailadmin-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.2}s`;
        card.classList.add('tailadmin-fade-in-up');
    });

    // Show welcome toast
    setTimeout(() => {
        showToast('Welcome to SantriMental! Choose your dashboard to get started.', 'success');
    }, 1000);
});
</script>
@endpush
