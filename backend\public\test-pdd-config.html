<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDD Config</title>
</head>
<body>
    <h1>Test PDD Configuration</h1>
    <div id="result"></div>

    <script src="js/form-configs.js"></script>
    <script>
        console.log('FormConfigs loaded:', typeof FormConfigs);
        console.log('Available configs:', Object.keys(FormConfigs || {}));
        
        if (FormConfigs && FormConfigs.PDD) {
            console.log('PDD config found:', FormConfigs.PDD);
            document.getElementById('result').innerHTML = `
                <h2>PDD Configuration Found!</h2>
                <p><strong>Name:</strong> ${FormConfigs.PDD.name}</p>
                <p><strong>Category:</strong> ${FormConfigs.PDD.category}</p>
                <p><strong>Questions:</strong> ${FormConfigs.PDD.questions.length}</p>
                <p><strong>Answer Options:</strong> ${FormConfigs.PDD.answer_options.length}</p>
            `;
        } else {
            console.error('PDD config not found');
            document.getElementById('result').innerHTML = `
                <h2 style="color: red;">PDD Configuration NOT Found!</h2>
                <p>Available configs: ${Object.keys(FormConfigs || {}).join(', ')}</p>
            `;
        }
    </script>
</body>
</html>
