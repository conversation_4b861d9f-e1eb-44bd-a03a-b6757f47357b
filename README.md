# 🚀 SantriMental Modules - Extracted Package

## 📋 **Overview**

Ini adalah package modul-modul SantriMental yang telah diekstrak dari instalasi Laravel lengkap. Package ini berisi semua komponen yang diperlukan untuk menginstal SantriMental pada fresh Laravel installation.

## 📁 **Structure**

```
santrimental-modules/
├── resources/views/           # Frontend Blade templates
├── public/js/                # JavaScript modules
├── public/css/               # CSS framework
├── app/Http/Controllers/     # API controllers
├── app/Models/               # Eloquent models
├── app/Http/Middleware/      # Custom middleware
├── database/migrations/      # Database schema
├── database/seeders/         # Sample data
├── routes/                   # Web & API routes
├── config/                   # Configuration files
└── install/                  # Installation scripts
```

## 🎯 **Modules Included**

### **Frontend Modules**
- ✅ **Modern Dashboard Views** - Glass morphism design
- ✅ **Multi-role Dashboards** - Admin, Guru, Siswa, Orang Tua
- ✅ **Assessment System** - Dynamic form rendering
- ✅ **History Management** - Assessment history viewer
- ✅ **Responsive Design** - Mobile-first approach

### **JavaScript Framework**
- ✅ **Authentication System** - Login/logout management
- ✅ **Dashboard Components** - Interactive dashboard elements
- ✅ **Form System** - Dynamic form rendering
- ✅ **Modern Components** - Modals, notifications, tooltips
- ✅ **Performance Optimizer** - Lazy loading, caching
- ✅ **Utility Functions** - Common helper functions

### **CSS Design System**
- ✅ **Modern Dashboard CSS** - Complete design framework
- ✅ **Glass Morphism** - Modern glass effects
- ✅ **Dark/Light Theme** - Theme switching support
- ✅ **Responsive Grid** - Mobile-responsive layout
- ✅ **Animation System** - Smooth transitions

### **Backend API**
- ✅ **Authentication Controllers** - Login, register, OAuth
- ✅ **Assessment Controllers** - Mental health assessments
- ✅ **Form Controllers** - Dynamic form management
- ✅ **Role Controllers** - Multi-role access control
- ✅ **Dashboard APIs** - Role-based dashboard data

### **Database Schema**
- ✅ **User Management** - Enhanced user model with roles
- ✅ **Assessment System** - Mental health assessment storage
- ✅ **Form Templates** - Dynamic form definitions
- ✅ **Response Storage** - Assessment response management
- ✅ **Relationship Management** - Student-parent relationships

## 🔧 **Installation Methods**

### **Method 1: Automated Installer**
1. Extract this package to your fresh Laravel project
2. Run `install.bat` (Windows) or `install.sh` (Linux/Mac)
3. Configure `.env` file
4. Run `php artisan migrate && php artisan db:seed`

### **Method 2: Manual Installation**
1. Copy files to respective Laravel directories
2. Update routes and middleware
3. Configure database
4. Run migrations and seeders

### **Method 3: Composer Package**
1. Add to composer.json as local package
2. Run `composer install`
3. Publish assets with `php artisan vendor:publish`

## 🎯 **Features**

### **Mental Health Platform**
- Multi-role dashboard system
- Dynamic assessment forms (SRQ-20, etc.)
- Real-time data visualization
- Assessment history tracking
- Parent-student relationship management

### **Modern UI/UX**
- Glass morphism design language
- Dark/Light theme support
- Responsive mobile-first design
- Smooth animations and transitions
- Interactive components

### **Technical Features**
- Role-based access control
- RESTful API architecture
- Modular component system
- Performance optimizations
- Security best practices

## 📋 **Requirements**

- PHP >= 8.1
- Laravel >= 10.0
- MySQL >= 8.0 or PostgreSQL >= 12
- Node.js >= 16 (for asset compilation)
- Composer

## 🚀 **Quick Start**

1. **Fresh Laravel Installation:**
   ```bash
   composer create-project laravel/laravel my-santrimental
   cd my-santrimental
   ```

2. **Install SantriMental Modules:**
   ```bash
   # Extract santrimental-modules to project root
   # Run installer
   ./install.bat  # Windows
   # or
   ./install.sh   # Linux/Mac
   ```

3. **Configure Environment:**
   ```bash
   # Update .env with database settings
   php artisan migrate
   php artisan db:seed
   ```

4. **Start Application:**
   ```bash
   php artisan serve
   ```

## 🔗 **URLs After Installation**

- **Home**: `http://localhost:8000`
- **Student Dashboard**: `http://localhost:8000/dashboard`
- **Admin Dashboard**: `http://localhost:8000/admin/dashboard`
- **Teacher Dashboard**: `http://localhost:8000/guru/dashboard`
- **Parent Dashboard**: `http://localhost:8000/orangtua/dashboard`
- **Assessments**: `http://localhost:8000/assessments`
- **History**: `http://localhost:8000/history`

## 👥 **Default Users**

After running seeders:
- **Admin**: <EMAIL> / password
- **Teacher**: <EMAIL> / password
- **Student**: <EMAIL> / password
- **Parent**: <EMAIL> / password

## 📞 **Support**

For installation issues or questions:
1. Check the installation guide
2. Verify all requirements are met
3. Check Laravel logs for errors
4. Ensure proper file permissions

## 📄 **License**

This package is open-sourced software licensed under the MIT license.

---

**SantriMental Team** - Mental Health Assessment Platform for Educational Institutions
