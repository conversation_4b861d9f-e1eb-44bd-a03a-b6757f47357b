Rancangan TOKEN PEDIA

Algoritma aplikasi "TOKEN PEDIA"

Pengembangan dari pesantren digital sebagai upaya peningkatan kesehatan jiwa dan kesejahteraan santri.

Bentuk: aplikasi android

Bagian-bagian dari aplikasi "TOKEN PEDIA"

Skrining kesehatan jiwa
Terapeutik Edukasi/Promosi Kesehatan Kolaboratif

1. Mental Health Knowledge Questionnaire (Kuesioner pengetahuan kesehatan jiwa)
2. Perceived Devaluation Discrimination (PDD) Stigma dan diskrimnasi masalah kesehatan jiwa
3. General Self-Efficacy (Kuesioner efikasi diri) 4. Mindfull self cares scale (MSCS) (Kuesioner perawatan diri) 5. Self Rating Questioannaire (SRQ) 20 6. Patient Health Questionnaire-9 1. Perawatan diri kesehatan jiwa 2. Modifikasi perilaku 1. E Modul Psikoedukasi kesehatan jiwa di pesantren 2. Modul digital Perawatan diri kesehatan jiwa di Pesantren 3. Link Game online Pencegahan bullying 4. Edukasi film Psikoedukasi
Film animasi 1. Perilaku mencari bantuan kesehatan jiwa formal 2. Perilaku mencari bantuan kesehatan jiwa informal

Rancangan TOKEN PEDIA

Gambaran algoritma
TOKEN PEDIA:
1. Aplikasi digital sebagai bentuk upaya pencegahan masalah kesehatan jiwa dan peningkatan kesejahteraan santri di pondok pesantern.
2. Token pedia sebagai bentuk gaya hidup sehat berkelanjutan dengan merubah perilaku untuk kesehatan jiwa dan kesejahteraan santri melalui program perawatan diri kesehatan jiwa di pesantren dan modifikasi perilaku gaya hidup sehat di pondok pesantern
3. Aplikasi ini menjadi bentuk logbook kegiatan bagi santri untuk tetap menjaga kesehatan jiwa di pondok pesantren

TOKEN PEDIA: Skrining kesehatan jiwa bagi santri terdiri dari:
1. MHKQ 2. PPD 3. GSE 4. MSCS 5. SRQ 20 6. DASS 42
Terapeutik: Aktivitas yang dapat dilakukan untuk proses menuju sehat dan sejahtera.

MHKQ (Mental Health Knowledge Questionnaire) : kuesioner yang digunakan untuk mengukur tingkat literasi kesehatan mental seseorang PDD (Perceived Devaluation Discrimination ) adalah alat ukur untuk menilai keyakinan seseorang bahwa dirinya diperlakukan negatif atau tidak adil karena karakteristik pribadi tertentu yang dimilikinya.
GSE (Genral self-efficacy) adalah alat ukur untuk menilai keyakinan umum seseorang tentang kemampuannya untuk mengatasi berbagai situasi dan tantangan dalam hidup.
MSCS (Mindful Self-Care Scale) alat ukur yang digunakan untuk mengidentifikasi area kekuatan dan kelemahan dalam praktik perawatan diri seseorang
SRQ 20 (Self-Reporting Questionnaire20)alat ukur yang digunakan untuk untuk skrining gangguan psikiatri, atau lebih tepatnya, untuk mendeteksi masalah kesehatan mental atau distres emosional yang tidak spesifik DASS 42 alat ukur mengidenfikasi masalah kesehatan jiwa seperti deprasi, kecemasan, dan stres
Perawatan diri kesehatan jiwa di Pesanteran
1.Perawatan diri fisik 2. Perawatan diri Emosional 3.Perawatan diri Spiritual

Klik
https://docs.google.co m/document/d/1qma5h
BfuzQMQqLA-
Klik
https://docs.google.com/d ocument/d/1XHCibeQfIne NOLPaNmwMmnuceasno
eWh/edit?usp=sharing& Klik
https://docs.google.com/d ocument/d/1IwNIrfQpcm
V5I0yM-t
Klik
https://docs.google.com/d ocument/d/1r5a9ZMIeRr9
JyA8FZJDIjVUx6keWgT-
Klik
https://docs.google.com/d ocument/d/1STjaeoqCylb dQShxkjg9FXYgzROvd1
dW/edit?usp=sharin Klik
https://docs.google.com/d ocument/d/13ki5rVNE__ H1Fd2UWU3eb_BaTyv6 GRVT/edit?usp=sharing& ouid=1061037934274210
54497&rtpof=true& Klik
https://drive.google.com/fi le/d/1M2LI_KCw3tgfl2G 91poCthJlyqlTryTB/view ?usp=sharing

Rancangan TOKEN PEDIA

Modifikasi perilaku
(rata-rata aktivitas yang dilakukan setiap hari) akan menjadi rapot setiap minggunya)

Edukasi/Promosi Kesehatan
Aktivitas dalam upaya pencegahan masalah kesehatan jiwa di pondok pesantren

Klik E-Modul Psikoedukasi Kesehatan jiwa di pondok pesantren
https://drive.google.com/file/d/139Me51gFfdJiwWZFFctZgv6rCxhFclkZ/view?usp=sharing
Klik E-Modul perawatan diri kesehatan jiwa di pondok pesantren
https://drive.google.com/file/d/1tyJU--saXDw5gARV7msA4voQCziVxD4W/view?usp=sharing
Klik Game permainan kesehatan jiwa https://mizu-izumi.itch.io/gen-zas
Klik Film seri psikoedukasi
https://drive.google.com/file/d/1iRiigoZncFO20XThcdQ_r588YfsAIXe/view?usp=s haring
https://drive.google.com/file/d/1avTWpPYSWu6Ldu0pQKhk5ML5TLA6jDaR/view ?usp=sharing
https://drive.google.com/file/d/149Esgh0M_Zf8wKg4cQpnpkVJ7MiYoaYz/view?u sp=sharing
https://drive.google.com/file/d/1d1GOjt027X7d7uWOSPVUxUrGAV61Dgh/view?usp=sharing

Kolaborasi
Perilaku mencari bantuan kesehatan jiwa di pondok pesantren melalui kolaborasi

Klik
Film animasi Perilaku mencari bantuan kesheatan jiwa di pondok pesantren
https://drive.google.com/file/d/1Oy285Boc4pE5mF2uLMd-gepAN6ti5sN/view?usp=sharing
https://drive.google.com/file/d/11NuU8o2bEJTC1oWelZFxMJC8TJIbkWf_/view? usp=sharing
https://drive.google.com/file/d/12x7yI1mCnFPqCfaxD1Z14MKiv8nWu6wh/view ?usp=sharing
https://drive.google.com/file/d/131vair3jNVIkdIeb_B9jjqHqSrF0wNs/view?usp= sharing

Rancangan TOKEN PEDIA
Link modul perawatan diri https://drive.google.com/file/d/1tyJU--saXDw5gARV7msA4voQCziVxD4W/view?usp=sharing
Sesi 1kesakralan dan filosofi pernikahan https://drive.google.com/file/d/1iRiig-oZncFO20XThcdQ_r588YfsAIXe/view?usp=sharing sesi 2 perawata dan pencegahan pernikahan muda https://drive.google.com/file/d/1avTWpPYSWu6Ldu0pQKhk5ML5TLA6jDaR/view?usp=sharing Sesi 3 manajemen stress dalam mencegah pernikahan muda melalui budaya local https://drive.google.com/file/d/149Esgh0M_Zf8wKg4cQpnpkVJ7MiYoaYz/view?usp=sharing Sesi 4 manajemen beban dalam pencegahan pernikhan muda melalui budaya local
https://drive.google.com/file/d/1d1GOjt027X7d7uWOSPVUxUrGAV6-1Dgh/view?usp=sharing
Sesi 5 pemberdayaan melalui pencegahan pernikahan muda melalui budaya local
Eps 1 teman sebaya https://drive.google.com/file/d/11NuU8o2bEJTC1oWelZFxMJC8TJIbkWf_/view?usp=sharing Eps 2 orang tua https://drive.google.com/file/d/12x7yI1mCnFPqCfaxD1Z14MKiv8nWu6wh/view?usp=sharing Eps 3 dokter https://drive.google.com/file/d/131vair3-jNVIkdIeb_B9jjqHqSrF0wNs/view?usp=sharing animasi mencari bantuan https://drive.google.com/file/d/1Oy285Boc4pE5mF2uLMd-gepAN6ti5sN-/view?usp=sharing

Rancangan TOKEN PEDIA Tabel modifikasi perilaku
Hari, tanggal bulan tahun

Aktivitas positif (uraikan) Aktivitas Negatif (Uraikan)

Hari Ketujuh

Kesimpulan dan rekomendasi

