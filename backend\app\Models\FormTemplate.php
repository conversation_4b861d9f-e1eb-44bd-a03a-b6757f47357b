<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'category',
        'questions',
        'scoring_rules',
        'interpretation_rules',
        'time_limit',
        'is_active',
        'version'
    ];

    protected $casts = [
        'questions' => 'array',
        'scoring_rules' => 'array',
        'interpretation_rules' => 'array',
        'is_active' => 'boolean'
    ];

    public function formQuestions()
    {
        return $this->hasMany(FormQuestion::class);
    }

    public function responses()
    {
        return $this->hasMany(FormResponse::class);
    }

    public function calculateScore($answers)
    {
        $scoringRules = $this->scoring_rules;
        $scoringType = $scoringRules['type'] ?? 'binary_sum';

        switch ($scoringType) {
            case 'binary_sum':
                return $this->calculateBinaryScore($answers, $scoringRules);

            case 'likert_sum':
                return $this->calculateLikertScore($answers, $scoringRules);

            case 'knowledge_sum':
                return $this->calculateKnowledgeScore($answers, $scoringRules);

            case 'dass_scale':
                return $this->calculateDassScore($answers, $scoringRules);

            default:
                return $this->calculateBinaryScore($answers, $scoringRules);
        }
    }

    private function calculateBinaryScore($answers, $scoringRules)
    {
        $score = 0;
        foreach ($answers as $questionNumber => $answer) {
            if (isset($scoringRules['questions'][$questionNumber])) {
                $questionRule = $scoringRules['questions'][$questionNumber];
                $score += $answer ? $questionRule['yes_score'] : $questionRule['no_score'];
            }
        }
        return $score;
    }

    private function calculateLikertScore($answers, $scoringRules)
    {
        $score = 0;
        foreach ($answers as $questionNumber => $answer) {
            if (isset($scoringRules['questions'][$questionNumber])) {
                $questionRule = $scoringRules['questions'][$questionNumber];
                $score += $answer * $questionRule['multiplier'];
            }
        }
        return $score;
    }

    private function calculateKnowledgeScore($answers, $scoringRules)
    {
        $score = 0;
        $answerKey = $scoringRules['answer_key'] ?? [];

        foreach ($answers as $questionNumber => $answer) {
            if (isset($answerKey[$questionNumber])) {
                if ($answerKey[$questionNumber] === $answer) {
                    $score += 1;
                }
            }
        }
        return $score;
    }

    private function calculateDassScore($answers, $scoringRules)
    {
        // For DASS, we calculate total score but also return subscale scores
        $totalScore = 0;
        $subscaleScores = [
            'depression' => 0,
            'anxiety' => 0,
            'stress' => 0
        ];

        $subscales = $scoringRules['subscales'] ?? [];

        foreach ($answers as $questionNumber => $answer) {
            $totalScore += (int)$answer;

            // Determine which subscale this question belongs to
            foreach ($subscales as $subscale => $questionNumbers) {
                if (in_array($questionNumber, $questionNumbers)) {
                    $subscaleScores[$subscale] += (int)$answer;
                    break;
                }
            }
        }

        // Store subscale scores for later use
        $this->subscale_scores = $subscaleScores;

        return $totalScore;
    }

    public function interpretScore($score)
    {
        $rules = $this->interpretation_rules;

        foreach ($rules as $rule) {
            if ($score >= $rule['min_score'] && $score <= $rule['max_score']) {
                return [
                    'status' => $rule['status'],
                    'interpretation' => $rule['interpretation'],
                    'recommendations' => $rule['recommendations']
                ];
            }
        }

        return [
            'status' => 'unknown',
            'interpretation' => 'Tidak dapat menentukan interpretasi',
            'recommendations' => 'Konsultasikan dengan profesional'
        ];
    }
}
