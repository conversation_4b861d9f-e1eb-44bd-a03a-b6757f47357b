<!DOCTYPE html>
<html lang="id" class="@yield('theme-class', 'dark')">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'SantriMental - Mental Health Platform')</title>

    <!-- TailAdmin CSS Framework -->
    <link rel="stylesheet" href="{{ asset('css/tailadmin.css') }}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3c50e0',
                            600: '#2e3fd4',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        secondary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#80caee',
                            600: '#6bb8e0',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        success: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#f56565',
                            600: '#e53e3e',
                            700: '#dc2626',
                            800: '#b91c1c',
                            900: '#991b1b',
                        },
                        gray: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e0',
                            400: '#a0aec0',
                            500: '#718096',
                            600: '#4a5568',
                            700: '#2d3748',
                            800: '#1a202c',
                            900: '#171923',
                        },
                        dark: {
                            100: '#1c2434',
                            200: '#24303f',
                            300: '#313d4a',
                            400: '#3c4858',
                            500: '#475569',
                            600: '#64748b',
                            700: '#94a3b8',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    boxShadow: {
                        'default': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'dropdown': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                    },
                    zIndex: {
                        '60': '60',
                        '70': '70',
                        '80': '80',
                        '90': '90',
                        '100': '100',
                        '9999': '9999',
                    }
                }
            }
        }
    </script>

    <style>
        /* Header Layout Fixes */
        .tailadmin-header {
            padding: 2rem 2rem !important;
            min-height: 120px !important;
        }

        .tailadmin-header > div {
            align-items: flex-start !important;
            gap: 1.5rem;
            min-height: 80px;
        }

        .tailadmin-header h1 {
            line-height: 1.2 !important;
            margin-bottom: 0.5rem !important;
            font-size: 2rem !important;
        }

        .tailadmin-header p {
            line-height: 1.4 !important;
            margin-top: 0.5rem !important;
            font-size: 0.875rem !important;
        }

        /* Breadcrumb spacing */
        .tailadmin-header .breadcrumb {
            margin-top: 0.75rem;
            margin-bottom: 0.5rem;
        }

        /* Header actions alignment */
        .tailadmin-header .flex.items-start.space-x-4 {
            align-items: flex-start !important;
            padding-top: 0.5rem;
            gap: 1rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .tailadmin-header {
                padding: 1.5rem 1rem !important;
                min-height: 100px !important;
            }
        }
    </style>

    @stack('styles')
</head>
<body class="bg-gray-100 dark:bg-slate-900 font-sans" x-data="{ sidebarOpen: false, darkMode: true }" :class="{ 'dark': darkMode }">

    @if(!isset($hideSidebar) || !$hideSidebar)
    <!-- Sidebar -->
    <aside class="tailadmin-sidebar" :class="{ 'mobile-open': sidebarOpen }" x-show="sidebarOpen || window.innerWidth >= 1024" x-transition>
        <!-- Sidebar Header -->
        <div class="tailadmin-sidebar-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center mr-3">
                        @yield('logo-icon')
                    </div>
                    <div>
                        <h1 class="text-lg font-bold tailadmin-text-primary">SantriMental</h1>
                        <p class="text-sm tailadmin-text-secondary">@yield('dashboard-subtitle', 'Dashboard')</p>
                    </div>
                </div>
                <button @click="sidebarOpen = false" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Sidebar Navigation -->
        <nav class="tailadmin-sidebar-nav">
            @yield('navigation')
        </nav>

        <!-- Sidebar Footer -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-dark-300">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center mr-3">
                        <span class="text-white text-sm font-bold">@yield('user-initials', 'U')</span>
                    </div>
                    <div>
                        <p class="text-sm font-medium tailadmin-text-primary">@yield('user-name', 'User')</p>
                        <p class="text-xs tailadmin-text-secondary">@yield('user-role', 'Role')</p>
                    </div>
                </div>
                <button @click="darkMode = !darkMode" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-200">
                    <svg x-show="!darkMode" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                    <svg x-show="darkMode" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </aside>

    <!-- Mobile Sidebar Overlay -->
    <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" x-transition></div>
    @endif

    <!-- Main Content -->
    <main class="@if(!isset($hideSidebar) || !$hideSidebar) tailadmin-main @else min-h-screen @endif">
        @if(!isset($hideHeader) || !$hideHeader)
        <!-- Header -->
        <header class="@if(!isset($hideSidebar) || !$hideSidebar) tailadmin-header @else bg-white dark:bg-slate-800 border-b border-gray-200 dark:border-dark-300 p-4 @endif">
            <div class="flex items-start justify-between min-h-[80px]">
                <div class="flex items-center flex-1">
                    @if(!isset($hideSidebar) || !$hideSidebar)
                    <button @click="sidebarOpen = true" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-200 mr-4 mt-1">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    @endif
                    <div class="flex-1">
                        <h1 class="text-2xl font-bold tailadmin-text-primary leading-tight">@yield('page-title', 'Dashboard')</h1>
                        <p class="tailadmin-text-secondary mt-1 text-sm leading-relaxed">@yield('page-description', 'Welcome to your dashboard')</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4 ml-4">
                    <div class="flex items-center space-x-3">
                        @yield('header-actions')
                    </div>

                    <!-- Notifications -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-200 relative">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 113.5 5.43l-4 4-4-4a6 6 0 113.5-5.43z"></path>
                            </svg>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-danger-500 rounded-full"></span>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-80 bg-white dark:bg-slate-800 rounded-lg shadow-dropdown border border-gray-200 dark:border-dark-300 z-50">
                            <div class="p-4 border-b border-gray-200 dark:border-dark-300">
                                <h3 class="font-semibold tailadmin-text-primary">Notifications</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto">
                                @yield('notifications', '<div class="p-4 text-center tailadmin-text-secondary">No notifications</div>')
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-200">
                            <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-semibold">@yield('user-initials', 'U')</span>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-medium tailadmin-text-primary">@yield('user-name', 'User')</p>
                                <p class="text-xs tailadmin-text-secondary">@yield('user-role', 'Role')</p>
                            </div>
                            <svg class="w-4 h-4 tailadmin-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-dropdown border border-gray-200 dark:border-dark-300 z-50">
                            <div class="p-2">
                                <a href="#" class="block px-3 py-2 text-sm tailadmin-text-primary hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg">Profile</a>
                                <a href="#" class="block px-3 py-2 text-sm tailadmin-text-primary hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg">Settings</a>
                                <hr class="my-2 border-gray-200 dark:border-dark-300">
                                <a href="#" class="block px-3 py-2 text-sm text-danger-500 hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            @hasSection('breadcrumb')
            <nav class="mt-6 flex items-center space-x-2 text-sm">
                @yield('breadcrumb')
            </nav>
            @endif
        </header>
        @endif

        <!-- Page Content -->
        <div class="@if(!isset($hideHeader) || !$hideHeader) mt-6 @endif">
            @yield('content')
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
        <div class="tailadmin-card p-8 text-center">
            <div class="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p class="tailadmin-text-primary font-medium">Loading...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Theme Management
        function initTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.classList.toggle('dark', savedTheme === 'dark');
            }
        }

        // Toast Notifications
        function showToast(message, type = 'info', duration = 5000) {
            const toast = document.createElement('div');
            const colors = {
                success: 'bg-success-500',
                error: 'bg-danger-500',
                warning: 'bg-warning-500',
                info: 'bg-primary-500'
            };
            
            toast.className = `${colors[type]} text-white px-6 py-4 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300`;
            toast.textContent = message;
            
            document.getElementById('toast-container').appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        // Loading Overlay
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            initTheme();
            
            // Watch for theme changes
            document.addEventListener('alpine:init', () => {
                Alpine.watch('darkMode', (value) => {
                    localStorage.setItem('theme', value ? 'dark' : 'light');
                });
            });
        });
    </script>

    @stack('scripts')
</body>
</html>



