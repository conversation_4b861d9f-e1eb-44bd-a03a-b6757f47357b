# 🧩 TOKEN PEDIA - <PERSON><PERSON><PERSON><PERSON><PERSON> CODE TEMPLATES
## AI-Assisted Code Generation Templates

---

## 🎯 TEMPLATE OVERVIEW

### **Purpose:**
- ✅ **Standardized Code Structure**: Consistent patterns across modules
- ✅ **AI-Friendly Templates**: Optimized for AI code generation
- ✅ **Rapid Development**: Quick scaffolding of new features
- ✅ **Best Practices**: Built-in security, testing, and performance patterns
- ✅ **Maintainable Code**: Clear separation of concerns

### **Usage with AI Tools:**
```bash
# Example AI Prompt:
"Generate a Laravel module for [MODULE_NAME] using the TOKEN PEDIA template structure. 
Include CRUD operations, validation, tests, and API endpoints following the established patterns."
```

---

## 🏗️ BACKEND TEMPLATES (Laravel)

### **1. MODULE STRUCTURE TEMPLATE**

#### **Directory Structure:**
```
app/Modules/[ModuleName]/
├── Controllers/
│   ├── Api/
│   │   └── [ModuleName]Controller.php
│   └── Web/
│       └── [ModuleName]Controller.php
├── Models/
│   └── [ModelName].php
├── Repositories/
│   ├── Contracts/
│   │   └── [ModuleName]RepositoryInterface.php
│   └── [ModuleName]Repository.php
├── Services/
│   └── [ModuleName]Service.php
├── Requests/
│   ├── Store[ModelName]Request.php
│   └── Update[ModelName]Request.php
├── Resources/
│   └── [ModelName]Resource.php
├── Policies/
│   └── [ModelName]Policy.php
├── Events/
│   └── [ModelName]Created.php
├── Listeners/
│   └── Send[ModelName]Notification.php
├── Jobs/
│   └── Process[ModelName].php
├── Migrations/
│   └── create_[table_name]_table.php
├── Seeders/
│   └── [ModelName]Seeder.php
├── Tests/
│   ├── Unit/
│   │   ├── [ModelName]Test.php
│   │   └── [ModuleName]ServiceTest.php
│   └── Feature/
│       └── [ModuleName]ApiTest.php
└── routes.php
```

### **2. MODEL TEMPLATE**

#### **AI Prompt for Model Generation:**
```
Generate a Laravel Eloquent model for [MODEL_NAME] with the following requirements:
- Fillable fields: [LIST_FIELDS]
- Relationships: [LIST_RELATIONSHIPS]
- Validation rules
- Accessors/Mutators if needed
- Soft deletes if applicable
- UUID primary key
- Timestamps
- Include proper PHPDoc comments
```

#### **Model Template:**
```php
<?php

namespace App\Modules\[ModuleName]\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * [ModelName] Model
 * 
 * @property string $id
 * @property string $name
 * @property string $description
 * @property bool $is_active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 */
class [ModelName] extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = '[table_name]';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * Validation rules for the model.
     */
    public static function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'id';
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Accessors & Mutators
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('d M Y H:i');
    }
}
```

### **3. CONTROLLER TEMPLATE**

#### **AI Prompt for Controller Generation:**
```
Generate a Laravel API controller for [MODEL_NAME] with:
- CRUD operations (index, store, show, update, destroy)
- Proper validation using Form Requests
- Resource transformations
- Error handling
- Authorization using policies
- Pagination for index
- Search and filtering capabilities
- API documentation comments
```

#### **API Controller Template:**
```php
<?php

namespace App\Modules\[ModuleName]\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Modules\[ModuleName]\Models\[ModelName];
use App\Modules\[ModuleName]\Requests\Store[ModelName]Request;
use App\Modules\[ModuleName]\Requests\Update[ModelName]Request;
use App\Modules\[ModuleName]\Resources\[ModelName]Resource;
use App\Modules\[ModuleName]\Services\[ModuleName]Service;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

/**
 * @group [ModuleName] Management
 * 
 * APIs for managing [module_name]
 */
class [ModuleName]Controller extends Controller
{
    public function __construct(
        private [ModuleName]Service $service
    ) {
        $this->middleware('auth:sanctum');
        $this->authorizeResource([ModelName]::class, '[model_name]');
    }

    /**
     * Display a listing of [model_name].
     * 
     * @queryParam search string Search term for filtering results.
     * @queryParam per_page integer Number of results per page. Default: 15
     * @queryParam sort_by string Field to sort by. Default: created_at
     * @queryParam sort_order string Sort order (asc|desc). Default: desc
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $[model_name_plural] = $this->service->getPaginated(
            search: $request->get('search'),
            perPage: $request->get('per_page', 15),
            sortBy: $request->get('sort_by', 'created_at'),
            sortOrder: $request->get('sort_order', 'desc')
        );

        return [ModelName]Resource::collection($[model_name_plural]);
    }

    /**
     * Store a newly created [model_name].
     */
    public function store(Store[ModelName]Request $request): JsonResponse
    {
        $[model_name] = $this->service->create($request->validated());

        return response()->json([
            'message' => '[ModelName] created successfully',
            'data' => new [ModelName]Resource($[model_name])
        ], 201);
    }

    /**
     * Display the specified [model_name].
     */
    public function show([ModelName] $[model_name]): JsonResponse
    {
        return response()->json([
            'data' => new [ModelName]Resource($[model_name])
        ]);
    }

    /**
     * Update the specified [model_name].
     */
    public function update(Update[ModelName]Request $request, [ModelName] $[model_name]): JsonResponse
    {
        $updated[ModelName] = $this->service->update($[model_name], $request->validated());

        return response()->json([
            'message' => '[ModelName] updated successfully',
            'data' => new [ModelName]Resource($updated[ModelName])
        ]);
    }

    /**
     * Remove the specified [model_name].
     */
    public function destroy([ModelName] $[model_name]): JsonResponse
    {
        $this->service->delete($[model_name]);

        return response()->json([
            'message' => '[ModelName] deleted successfully'
        ]);
    }
}
```

### **4. SERVICE TEMPLATE**

#### **AI Prompt for Service Generation:**
```
Generate a Laravel service class for [MODEL_NAME] with:
- CRUD operations
- Business logic separation
- Error handling
- Event dispatching
- Caching where appropriate
- Search and filtering logic
- Proper type hints and return types
```

#### **Service Template:**
```php
<?php

namespace App\Modules\[ModuleName]\Services;

use App\Modules\[ModuleName]\Models\[ModelName];
use App\Modules\[ModuleName]\Repositories\Contracts\[ModuleName]RepositoryInterface;
use App\Modules\[ModuleName]\Events\[ModelName]Created;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class [ModuleName]Service
{
    public function __construct(
        private [ModuleName]RepositoryInterface $repository
    ) {}

    /**
     * Get paginated [model_name_plural] with search and filtering.
     */
    public function getPaginated(
        ?string $search = null,
        int $perPage = 15,
        string $sortBy = 'created_at',
        string $sortOrder = 'desc'
    ): LengthAwarePaginator {
        return $this->repository->getPaginated($search, $perPage, $sortBy, $sortOrder);
    }

    /**
     * Get all active [model_name_plural].
     */
    public function getActive(): Collection
    {
        return Cache::remember(
            '[model_name_plural]_active',
            now()->addHours(1),
            fn() => $this->repository->getActive()
        );
    }

    /**
     * Create a new [model_name].
     */
    public function create(array $data): [ModelName]
    {
        try {
            DB::beginTransaction();

            $[model_name] = $this->repository->create($data);

            // Dispatch event
            event(new [ModelName]Created($[model_name]));

            // Clear cache
            Cache::forget('[model_name_plural]_active');

            DB::commit();

            Log::info('[ModelName] created', ['id' => $[model_name]->id]);

            return $[model_name];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create [model_name]', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Update an existing [model_name].
     */
    public function update([ModelName] $[model_name], array $data): [ModelName]
    {
        try {
            DB::beginTransaction();

            $updated[ModelName] = $this->repository->update($[model_name], $data);

            // Clear cache
            Cache::forget('[model_name_plural]_active');

            DB::commit();

            Log::info('[ModelName] updated', ['id' => $[model_name]->id]);

            return $updated[ModelName];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update [model_name]', [
                'id' => $[model_name]->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Delete a [model_name].
     */
    public function delete([ModelName] $[model_name]): bool
    {
        try {
            DB::beginTransaction();

            $result = $this->repository->delete($[model_name]);

            // Clear cache
            Cache::forget('[model_name_plural]_active');

            DB::commit();

            Log::info('[ModelName] deleted', ['id' => $[model_name]->id]);

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete [model_name]', [
                'id' => $[model_name]->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Find [model_name] by ID.
     */
    public function findById(string $id): ?[ModelName]
    {
        return $this->repository->findById($id);
    }

    /**
     * Search [model_name_plural] by term.
     */
    public function search(string $term): Collection
    {
        return $this->repository->search($term);
    }
}
```

### **5. TEST TEMPLATE**

#### **AI Prompt for Test Generation:**
```
Generate comprehensive PHPUnit tests for [MODEL_NAME] including:
- Model tests (relationships, scopes, attributes)
- Service tests (all methods, edge cases)
- API tests (all endpoints, validation, authorization)
- Factory and seeder tests
- Use proper test structure and assertions
```

#### **Feature Test Template:**
```php
<?php

namespace Tests\Feature\Modules\[ModuleName];

use App\Modules\[ModuleName]\Models\[ModelName];
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class [ModuleName]ApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);
    }

    /** @test */
    public function it_can_list_[model_name_plural](): void
    {
        [ModelName]::factory()->count(3)->create();

        $response = $this->getJson('/api/[model_name_plural]');

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'is_active',
                        'created_at',
                        'updated_at'
                    ]
                ],
                'links',
                'meta'
            ]);
    }

    /** @test */
    public function it_can_create_[model_name](): void
    {
        $data = [
            'name' => $this->faker->name,
            'description' => $this->faker->sentence,
            'is_active' => true
        ];

        $response = $this->postJson('/api/[model_name_plural]', $data);

        $response->assertCreated()
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'description',
                    'is_active'
                ]
            ]);

        $this->assertDatabaseHas('[table_name]', $data);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_[model_name](): void
    {
        $response = $this->postJson('/api/[model_name_plural]', []);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['name']);
    }

    /** @test */
    public function it_can_show_[model_name](): void
    {
        $[model_name] = [ModelName]::factory()->create();

        $response = $this->getJson("/api/[model_name_plural]/{$[model_name]->id}");

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'description',
                    'is_active'
                ]
            ]);
    }

    /** @test */
    public function it_can_update_[model_name](): void
    {
        $[model_name] = [ModelName]::factory()->create();
        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated Description'
        ];

        $response = $this->putJson("/api/[model_name_plural]/{$[model_name]->id}", $updateData);

        $response->assertOk()
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'name',
                    'description'
                ]
            ]);

        $this->assertDatabaseHas('[table_name]', $updateData);
    }

    /** @test */
    public function it_can_delete_[model_name](): void
    {
        $[model_name] = [ModelName]::factory()->create();

        $response = $this->deleteJson("/api/[model_name_plural]/{$[model_name]->id}");

        $response->assertOk()
            ->assertJson(['message' => '[ModelName] deleted successfully']);

        $this->assertSoftDeleted('[table_name]', ['id' => $[model_name]->id]);
    }

    /** @test */
    public function it_can_search_[model_name_plural](): void
    {
        $searchable[ModelName] = [ModelName]::factory()->create(['name' => 'Searchable Item']);
        [ModelName]::factory()->create(['name' => 'Other Item']);

        $response = $this->getJson('/api/[model_name_plural]?search=Searchable');

        $response->assertOk();
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals($searchable[ModelName]->id, $response->json('data.0.id'));
    }

    /** @test */
    public function unauthorized_user_cannot_access_[model_name_plural](): void
    {
        Sanctum::actingAs(User::factory()->create(), []);

        $response = $this->getJson('/api/[model_name_plural]');

        $response->assertForbidden();
    }
}
```

---

## 🎨 FRONTEND TEMPLATES (React + TypeScript)

### **1. COMPONENT STRUCTURE TEMPLATE**

#### **Directory Structure:**
```
src/modules/[module-name]/
├── components/
│   ├── [ComponentName].tsx
│   ├── [ComponentName].test.tsx
│   └── index.ts
├── hooks/
│   ├── use[ModuleName].ts
│   ├── use[ModuleName]Mutations.ts
│   └── index.ts
├── services/
│   ├── [module-name].service.ts
│   ├── [module-name].types.ts
│   └── index.ts
├── pages/
│   ├── [ModuleName]List.tsx
│   ├── [ModuleName]Detail.tsx
│   ├── [ModuleName]Form.tsx
│   └── index.ts
├── store/
│   ├── [module-name].store.ts
│   └── index.ts
└── index.ts
```

### **2. REACT COMPONENT TEMPLATE**

#### **AI Prompt for Component Generation:**
```
Generate a React TypeScript component for [COMPONENT_NAME] with:
- TypeScript interfaces for props
- Proper error handling
- Loading states
- Accessibility features
- Responsive design with Tailwind CSS
- Unit tests
- Storybook stories if needed
```

#### **Component Template:**
```tsx
import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { [ModelName] } from '../services/[module-name].types';
import { [module-name]Service } from '../services/[module-name].service';
import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import { ErrorMessage } from '@/shared/components/ErrorMessage';

interface [ComponentName]Props {
  [model-name]Id?: string;
  onSuccess?: ([model-name]: [ModelName]) => void;
  onError?: (error: Error) => void;
  className?: string;
}

export const [ComponentName]: React.FC<[ComponentName]Props> = ({
  [model-name]Id,
  onSuccess,
  onError,
  className = ''
}) => {
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Query for fetching data
  const {
    data: [model-name],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['[model-name]', [model-name]Id],
    queryFn: () => [module-name]Service.getById([model-name]Id!),
    enabled: !![model-name]Id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation for updates
  const updateMutation = useMutation({
    mutationFn: [module-name]Service.update,
    onSuccess: (updated[ModelName]) => {
      queryClient.invalidateQueries({ queryKey: ['[model-name-plural]'] });
      toast.success('[ModelName] updated successfully');
      onSuccess?.(updated[ModelName]);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update [model-name]');
      onError?.(error);
    },
  });

  const handleSubmit = async (data: Partial<[ModelName]>) => {
    if (!data || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await updateMutation.mutateAsync({
        id: [model-name]Id!,
        ...data
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorMessage
        message="Failed to load [model-name]"
        onRetry={refetch}
      />
    );
  }

  return (
    <div className={`[component-name] ${className}`}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            [Component Title]
          </h2>
          {isSubmitting && (
            <LoadingSpinner size="sm" />
          )}
        </div>

        {/* Component content */}
        <div className="space-y-4">
          {[model-name] && (
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {[model-name].name}
              </h3>
              <p className="text-gray-600 mt-1">
                {[model-name].description}
              </p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => handleSubmit({})}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default [ComponentName];
```

---

### **3. CUSTOM HOOK TEMPLATE**

#### **AI Prompt for Hook Generation:**
```
Generate a custom React hook for [HOOK_NAME] with:
- TypeScript interfaces
- React Query integration
- Error handling
- Loading states
- Optimistic updates
- Proper cleanup
```

#### **Hook Template:**
```tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { [ModelName], Create[ModelName]Data, Update[ModelName]Data } from '../services/[module-name].types';
import { [module-name]Service } from '../services/[module-name].service';

interface Use[ModuleName]Options {
  enabled?: boolean;
  onSuccess?: ([model-name]: [ModelName]) => void;
  onError?: (error: Error) => void;
}

interface Use[ModuleName]Return {
  // Data
  [model-name-plural]: [ModelName][] | undefined;
  [model-name]: [ModelName] | undefined;

  // States
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: Error | null;

  // Actions
  create: (data: Create[ModelName]Data) => Promise<[ModelName]>;
  update: (id: string, data: Update[ModelName]Data) => Promise<[ModelName]>;
  remove: (id: string) => Promise<void>;
  refetch: () => void;

  // Utilities
  getById: (id: string) => [ModelName] | undefined;
  search: (term: string) => [ModelName][];
}

export const use[ModuleName] = (
  [model-name]Id?: string,
  options: Use[ModuleName]Options = {}
): Use[ModuleName]Return => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');

  // Query for list
  const {
    data: [model-name-plural],
    isLoading: isLoadingList,
    error: listError,
    refetch: refetchList
  } = useQuery({
    queryKey: ['[model-name-plural]'],
    queryFn: [module-name]Service.getAll,
    enabled: options.enabled !== false,
    staleTime: 5 * 60 * 1000,
  });

  // Query for single item
  const {
    data: [model-name],
    isLoading: isLoadingSingle,
    error: singleError,
    refetch: refetchSingle
  } = useQuery({
    queryKey: ['[model-name]', [model-name]Id],
    queryFn: () => [module-name]Service.getById([model-name]Id!),
    enabled: !![model-name]Id && options.enabled !== false,
    staleTime: 5 * 60 * 1000,
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: [module-name]Service.create,
    onSuccess: (new[ModelName]) => {
      queryClient.invalidateQueries({ queryKey: ['[model-name-plural]'] });
      toast.success('[ModelName] created successfully');
      options.onSuccess?.(new[ModelName]);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create [model-name]');
      options.onError?.(error);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Update[ModelName]Data }) =>
      [module-name]Service.update(id, data),
    onSuccess: (updated[ModelName]) => {
      queryClient.invalidateQueries({ queryKey: ['[model-name-plural]'] });
      queryClient.invalidateQueries({ queryKey: ['[model-name]', updated[ModelName].id] });
      toast.success('[ModelName] updated successfully');
      options.onSuccess?.(updated[ModelName]);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update [model-name]');
      options.onError?.(error);
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: [module-name]Service.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['[model-name-plural]'] });
      toast.success('[ModelName] deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete [model-name]');
      options.onError?.(error);
    },
  });

  // Actions
  const create = useCallback(
    async (data: Create[ModelName]Data): Promise<[ModelName]> => {
      return createMutation.mutateAsync(data);
    },
    [createMutation]
  );

  const update = useCallback(
    async (id: string, data: Update[ModelName]Data): Promise<[ModelName]> => {
      return updateMutation.mutateAsync({ id, data });
    },
    [updateMutation]
  );

  const remove = useCallback(
    async (id: string): Promise<void> => {
      return deleteMutation.mutateAsync(id);
    },
    [deleteMutation]
  );

  const refetch = useCallback(() => {
    refetchList();
    if ([model-name]Id) {
      refetchSingle();
    }
  }, [refetchList, refetchSingle, [model-name]Id]);

  // Utilities
  const getById = useCallback(
    (id: string): [ModelName] | undefined => {
      return [model-name-plural]?.find(item => item.id === id);
    },
    [[model-name-plural]]
  );

  const search = useCallback(
    (term: string): [ModelName][] => {
      if (!term || ![model-name-plural]) return [model-name-plural] || [];

      return [model-name-plural].filter(item =>
        item.name.toLowerCase().includes(term.toLowerCase()) ||
        item.description?.toLowerCase().includes(term.toLowerCase())
      );
    },
    [[model-name-plural]]
  );

  return {
    // Data
    [model-name-plural],
    [model-name],

    // States
    isLoading: isLoadingList || isLoadingSingle,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    error: listError || singleError,

    // Actions
    create,
    update,
    remove,
    refetch,

    // Utilities
    getById,
    search,
  };
};
```

### **4. SERVICE TEMPLATE**

#### **Service Template:**
```tsx
import { ApiClient } from '@/shared/services/api-client';
import { PaginatedResponse, ApiResponse } from '@/shared/types/api.types';
import { [ModelName], Create[ModelName]Data, Update[ModelName]Data } from './[module-name].types';

class [ModuleName]Service {
  private readonly baseUrl = '/[model-name-plural]';

  async getAll(): Promise<[ModelName][]> {
    const response = await ApiClient.get<ApiResponse<[ModelName][]>>(this.baseUrl);
    return response.data.data;
  }

  async getPaginated(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<[ModelName]>> {
    const response = await ApiClient.get<PaginatedResponse<[ModelName]>>(
      this.baseUrl,
      { params }
    );
    return response.data;
  }

  async getById(id: string): Promise<[ModelName]> {
    const response = await ApiClient.get<ApiResponse<[ModelName]>>(`${this.baseUrl}/${id}`);
    return response.data.data;
  }

  async create(data: Create[ModelName]Data): Promise<[ModelName]> {
    const response = await ApiClient.post<ApiResponse<[ModelName]>>(this.baseUrl, data);
    return response.data.data;
  }

  async update(id: string, data: Update[ModelName]Data): Promise<[ModelName]> {
    const response = await ApiClient.put<ApiResponse<[ModelName]>>(`${this.baseUrl}/${id}`, data);
    return response.data.data;
  }

  async delete(id: string): Promise<void> {
    await ApiClient.delete(`${this.baseUrl}/${id}`);
  }

  async search(term: string): Promise<[ModelName][]> {
    const response = await ApiClient.get<ApiResponse<[ModelName][]>>(
      `${this.baseUrl}/search`,
      { params: { q: term } }
    );
    return response.data.data;
  }
}

export const [module-name]Service = new [ModuleName]Service();
```

### **5. TYPE DEFINITIONS TEMPLATE**

#### **Types Template:**
```tsx
// Base model interface
export interface [ModelName] {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// Create data interface
export interface Create[ModelName]Data {
  name: string;
  description?: string;
  is_active?: boolean;
}

// Update data interface
export interface Update[ModelName]Data {
  name?: string;
  description?: string;
  is_active?: boolean;
}

// Form data interface
export interface [ModelName]FormData extends Create[ModelName]Data {
  id?: string;
}

// Filter interface
export interface [ModelName]Filters {
  search?: string;
  is_active?: boolean;
  created_from?: string;
  created_to?: string;
}

// Sort options
export type [ModelName]SortField = 'name' | 'created_at' | 'updated_at';
export type SortOrder = 'asc' | 'desc';

export interface [ModelName]SortOptions {
  field: [ModelName]SortField;
  order: SortOrder;
}

// List view interface
export interface [ModelName]ListItem extends Pick<[ModelName], 'id' | 'name' | 'is_active' | 'created_at'> {
  description_preview?: string;
}

// Detail view interface
export interface [ModelName]Detail extends [ModelName] {
  // Additional fields for detail view
  metadata?: Record<string, any>;
  relationships?: {
    // Related entities
  };
}
```

---

## 📱 MOBILE TEMPLATES (Flutter)

### **1. WIDGET STRUCTURE TEMPLATE**

#### **Directory Structure:**
```
lib/modules/[module_name]/
├── models/
│   └── [model_name].dart
├── services/
│   └── [module_name]_service.dart
├── repositories/
│   └── [module_name]_repository.dart
├── blocs/
│   ├── [module_name]_bloc.dart
│   ├── [module_name]_event.dart
│   └── [module_name]_state.dart
├── widgets/
│   ├── [module_name]_list.dart
│   ├── [module_name]_item.dart
│   └── [module_name]_form.dart
├── screens/
│   ├── [module_name]_list_screen.dart
│   ├── [module_name]_detail_screen.dart
│   └── [module_name]_form_screen.dart
└── [module_name]_module.dart
```

### **2. FLUTTER WIDGET TEMPLATE**

#### **AI Prompt for Widget Generation:**
```
Generate a Flutter widget for [WIDGET_NAME] with:
- Proper state management using Bloc
- Error handling and loading states
- Responsive design
- Accessibility features
- Unit tests
- Material Design 3 components
```

#### **Widget Template:**
```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:token_pedia_mobile/core/widgets/loading_widget.dart';
import 'package:token_pedia_mobile/core/widgets/error_widget.dart';
import 'package:token_pedia_mobile/modules/[module_name]/blocs/[module_name]_bloc.dart';
import 'package:token_pedia_mobile/modules/[module_name]/models/[model_name].dart';

class [WidgetName] extends StatelessWidget {
  final String? [model_name]Id;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;

  const [WidgetName]({
    Key? key,
    this.[model_name]Id,
    this.onTap,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<[ModuleName]Bloc, [ModuleName]State>(
      builder: (context, state) {
        if (state is [ModuleName]Loading) {
          return const LoadingWidget();
        }

        if (state is [ModuleName]Error) {
          return ErrorWidget(
            message: state.message,
            onRetry: () => context.read<[ModuleName]Bloc>().add(
              Load[ModuleName]([model_name]Id: [model_name]Id),
            ),
          );
        }

        if (state is [ModuleName]Loaded) {
          return _buildContent(context, state.[model_name]);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildContent(BuildContext context, [ModelName] [model_name]) {
    final theme = Theme.of(context);

    return Container(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: Card(
        elevation: 2,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        [model_name].name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if ([model_name].isActive)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Active',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ),
                  ],
                ),
                if ([model_name].description != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    [model_name].description!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate([model_name].createdAt),
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
```

---

## 🔧 AI GENERATION COMMANDS

### **Quick Generation Commands:**

#### **Backend Module Generation:**
```bash
# Generate complete Laravel module
ai generate laravel-module --name=[ModuleName] --fields="name:string,description:text,is_active:boolean" --relationships="belongsTo:User"

# Generate specific components
ai generate laravel-controller --module=[ModuleName] --type=api
ai generate laravel-service --module=[ModuleName]
ai generate laravel-test --module=[ModuleName] --type=feature
```

#### **Frontend Component Generation:**
```bash
# Generate React component
ai generate react-component --name=[ComponentName] --module=[ModuleName] --props="id:string,onSuccess:function"

# Generate custom hook
ai generate react-hook --name=use[ModuleName] --features="query,mutation,cache"

# Generate service
ai generate react-service --name=[ModuleName]Service --endpoints="crud,search"
```

#### **Mobile Widget Generation:**
```bash
# Generate Flutter widget
ai generate flutter-widget --name=[WidgetName] --module=[ModuleName] --state-management=bloc

# Generate Bloc
ai generate flutter-bloc --name=[ModuleName]Bloc --events="load,create,update,delete"

# Generate model
ai generate flutter-model --name=[ModelName] --fields="id:String,name:String,isActive:bool"
```

### **Template Usage Examples:**

#### **Creating Assessment Module:**
```bash
# 1. Generate backend
ai generate laravel-module --name=Assessment --fields="title:string,questions:json,scoring_rules:json,interpretation_rules:json" --relationships="hasMany:FormResponse"

# 2. Generate frontend
ai generate react-component --name=AssessmentForm --module=assessment --props="assessmentId:string,onComplete:function"

# 3. Generate mobile
ai generate flutter-widget --name=AssessmentWidget --module=assessment --state-management=bloc
```

---

**Template sistem ini memungkinkan pengembangan yang cepat, konsisten, dan berkualitas tinggi dengan bantuan AI tools untuk TOKEN PEDIA project.**
