/**
 * Modern Dashboard JavaScript Framework
 * Provides utilities for modern UI interactions, animations, and state management
 */

class ModernDashboard {
    constructor() {
        this.theme = localStorage.getItem('dashboard-theme') || 'dark';
        this.sidebarOpen = false;
        this.notifications = [];
        this.init();
    }

    init() {
        this.initTheme();
        this.initSidebar();
        this.initAnimations();
        this.initNotifications();
        this.initModals();
        this.initTooltips();
        this.bindEvents();
    }

    // Enhanced Theme Management
    initTheme() {
        // Check for system preference if no saved theme
        if (!localStorage.getItem('dashboard-theme')) {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.theme = prefersDark ? 'dark' : 'light';
        }

        document.documentElement.setAttribute('data-theme', this.theme);
        this.updateThemeToggle();
        this.updateChartThemes();

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('dashboard-theme')) {
                this.theme = e.matches ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', this.theme);
                this.updateThemeToggle();
                this.updateChartThemes();
            }
        });
    }

    toggleTheme() {
        const oldTheme = this.theme;
        this.theme = this.theme === 'dark' ? 'light' : 'dark';

        // Add transition class for smooth theme change
        document.body.classList.add('theme-transitioning');

        document.documentElement.setAttribute('data-theme', this.theme);
        localStorage.setItem('dashboard-theme', this.theme);

        this.updateThemeToggle();
        this.updateChartThemes();

        // Show theme change notification
        this.showNotification(
            `Switched to ${this.theme} mode`,
            'success',
            2000,
            {
                description: `Interface updated with ${this.theme} theme colors`
            }
        );

        // Remove transition class after animation
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 300);

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { oldTheme, newTheme: this.theme }
        }));
    }

    updateThemeToggle() {
        const toggles = document.querySelectorAll('#theme-toggle, [data-theme-toggle]');
        toggles.forEach(toggle => {
            if (toggle) {
                const icon = toggle.querySelector('i[data-lucide]');
                if (icon) {
                    // Update Lucide icon
                    icon.setAttribute('data-lucide', this.theme === 'dark' ? 'sun' : 'moon');
                    lucide.createIcons();
                }

                // Update tooltip
                toggle.setAttribute('data-tooltip',
                    this.theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'
                );

                // Add visual feedback
                toggle.classList.add('theme-toggle');
            }
        });
    }

    updateChartThemes() {
        // Update Chart.js default colors based on theme
        if (typeof Chart !== 'undefined') {
            if (this.theme === 'dark') {
                Chart.defaults.color = 'rgba(255, 255, 255, 0.7)';
                Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
                Chart.defaults.backgroundColor = 'rgba(255, 255, 255, 0.05)';
            } else {
                Chart.defaults.color = 'rgba(15, 23, 42, 0.7)';
                Chart.defaults.borderColor = 'rgba(15, 23, 42, 0.1)';
                Chart.defaults.backgroundColor = 'rgba(15, 23, 42, 0.05)';
            }

            // Trigger chart updates if they exist
            window.dispatchEvent(new CustomEvent('chartThemeUpdate'));
        }
    }

    getThemeColors() {
        const isDark = this.theme === 'dark';
        return {
            primary: '#0ea5e9',
            secondary: '#d946ef',
            success: '#22c55e',
            warning: '#f59e0b',
            error: '#ef4444',
            background: isDark ? '#1e293b' : '#ffffff',
            surface: isDark ? '#334155' : '#f8fafc',
            text: isDark ? '#f8fafc' : '#0f172a',
            textSecondary: isDark ? '#cbd5e1' : '#334155',
            border: isDark ? '#475569' : '#e2e8f0'
        };
    }

    // Sidebar Management
    initSidebar() {
        this.updateSidebarState();
    }

    toggleSidebar() {
        this.sidebarOpen = !this.sidebarOpen;
        this.updateSidebarState();
    }

    updateSidebarState() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-overlay');
        
        if (sidebar) {
            if (this.sidebarOpen) {
                sidebar.classList.add('open');
                if (overlay) overlay.classList.remove('hidden');
            } else {
                sidebar.classList.remove('open');
                if (overlay) overlay.classList.add('hidden');
            }
        }
    }

    setActiveSidebarItem(itemId) {
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to selected item
        const activeItem = document.getElementById(itemId);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    // Animation System
    initAnimations() {
        this.observeElements();
    }

    observeElements() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.stats-card, .chart-container, .modern-card').forEach(el => {
            observer.observe(el);
        });
    }

    animateValue(element, start, end, duration = 1000) {
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.round(start + (end - start) * easeOutQuart);
            
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        requestAnimationFrame(animate);
    }

    // Notification System
    initNotifications() {
        this.createNotificationContainer();
    }

    createNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.pointerEvents = 'auto';
        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-1">
                    <p class="font-medium">${message}</p>
                </div>
                <button class="ml-4 text-white/70 hover:text-white" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
        `;

        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, duration);

        return notification;
    }

    // Modal System
    initModals() {
        this.bindModalEvents();
    }

    bindModalEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target);
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal-overlay.show');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modal) {
        if (typeof modal === 'string') {
            modal = document.getElementById(modal);
        }
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // Tooltip System
    initTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            this.createTooltip(element);
        });
    }

    createTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = element.getAttribute('data-tooltip');
        tooltip.style.cssText = `
            position: absolute;
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 9999;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        `;

        document.body.appendChild(tooltip);

        element.addEventListener('mouseenter', (e) => {
            const rect = e.target.getBoundingClientRect();
            tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
            tooltip.style.opacity = '1';
        });

        element.addEventListener('mouseleave', () => {
            tooltip.style.opacity = '0';
        });
    }

    // Utility Methods
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }

    formatTime(date) {
        return new Intl.DateTimeFormat('id-ID', {
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }

    // Event Binding
    bindEvents() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => this.toggleSidebar());
        }

        // Mobile overlay
        const mobileOverlay = document.getElementById('mobile-overlay');
        if (mobileOverlay) {
            mobileOverlay.addEventListener('click', () => this.toggleSidebar());
        }

        // Sidebar items
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (window.innerWidth < 1024) {
                    this.toggleSidebar();
                }
            });
        });
    }

    // Loading States
    showLoading(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (element) {
            element.innerHTML = `
                <div class="flex items-center justify-center p-8">
                    <div class="loading-spinner"></div>
                    <span class="ml-2 text-sm text-gray-500">Loading...</span>
                </div>
            `;
        }
    }

    hideLoading(element, content) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (element) {
            element.innerHTML = content;
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernDashboard = new ModernDashboard();

    // Initialize Lucide icons if available
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Initialize additional SantriMental features
    initializeDateTime();
    initializeMobileMenu();

    console.log('🎉 SantriMental Dashboard Ready!');
});

// Additional utility functions for SantriMental
function initializeDateTime() {
    function updateDateTime() {
        const now = new Date();
        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');

        if (dateElement) {
            dateElement.textContent = now.toLocaleDateString('id-ID', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    updateDateTime();
    setInterval(updateDateTime, 1000);
}

function initializeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobile-overlay');

    if (mobileMenuBtn && sidebar && overlay) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });

        overlay.addEventListener('click', function() {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });
    }
}

function logout() {
    if (confirm('Apakah Anda yakin ingin keluar?')) {
        if (window.modernDashboard) {
            window.modernDashboard.showNotification('Logging out...', 'info');
        }
        setTimeout(() => {
            window.location.href = '/';
        }, 1000);
    }
}

// Make functions globally available
window.logout = logout;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernDashboard;
}
