<?php

use App\Http\Controllers\Api\AuthController;
// use App\Http\Controllers\Api\AssessmentController; // Legacy controller - using modular controller instead
use App\Http\Controllers\Api\FormTemplateController;
use App\Http\Controllers\Api\RoleController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// AUTHENTICATION TEMPORARILY DISABLED FOR TESTING
// All routes are now public for development/testing purposes

// Public routes - MOVED TO AUTH MODULE
// These routes are now handled by App\Modules\Auth\Http\Controllers\Api\AuthController
// Route::post('/register', [AuthController::class, 'register']); // DISABLED - Use /api/auth/register
// Route::post('/login', [AuthController::class, 'login']); // DISABLED - Use /api/auth/login
// Route::post('/auth/google', [AuthController::class, 'googleAuth']); // DISABLED - Use /api/auth/google
Route::post('/auth/qr', [AuthController::class, 'qrLogin']); // Keep QR login for now

// Previously protected routes - NOW PUBLIC FOR TESTING
// Route::middleware('auth:sanctum')->group(function () { // COMMENTED OUT FOR TESTING
// Auth routes - MOVED TO AUTH MODULE
// Route::get('/user', [AuthController::class, 'user']); // DISABLED - Use /api/auth/user
// Route::post('/logout', [AuthController::class, 'logout']); // DISABLED - Use /api/auth/logout

// Assessment routes (legacy) - Commented out in favor of modular routes
// Route::prefix('assessments')->group(function () {
//     Route::get('/', [AssessmentController::class, 'index']);
//     Route::post('/', [AssessmentController::class, 'store']);
//     Route::get('/questions', [AssessmentController::class, 'questions']);
//     Route::get('/dashboard-stats', [AssessmentController::class, 'dashboardStats']);
//     Route::get('/monthly-report', [AssessmentController::class, 'monthlyReport']);
//     Route::get('/{assessment}', [AssessmentController::class, 'show']);
// });

// Dynamic Form System routes - NOW PUBLIC
Route::prefix('forms')->group(function () {
    Route::get('/', [FormTemplateController::class, 'index']);
    Route::get('/{code}', [FormTemplateController::class, 'show']);
    Route::get('/{code}/responses', [FormTemplateController::class, 'getUserResponses']);
    Route::post('/{code}/submit', [FormTemplateController::class, 'submitResponse']); // Now public
});

// Dashboard stats (new dynamic system) - NOW PUBLIC
Route::get('/dashboard/stats', [FormTemplateController::class, 'getDashboardStats']);

// Role-based routes - NOW PUBLIC
Route::get('/user/role', [RoleController::class, 'getUserRole']);
Route::get('/dashboard/role-data', [RoleController::class, 'getDashboardData']);

// Admin routes - NOW PUBLIC (ROLE MIDDLEWARE REMOVED)
Route::get('/admin/users', function () {
    return response()->json(['message' => 'Admin users endpoint - Authentication disabled']);
});
Route::get('/admin/reports', function () {
    return response()->json(['message' => 'Admin reports endpoint - Authentication disabled']);
});

// Guru routes - NOW PUBLIC (ROLE MIDDLEWARE REMOVED)
Route::get('/guru/students', function () {
    return response()->json(['message' => 'Guru students endpoint - Authentication disabled']);
});
Route::get('/guru/class-reports', function () {
    return response()->json(['message' => 'Guru class reports endpoint - Authentication disabled']);
});

// Orangtua routes - NOW PUBLIC (ROLE MIDDLEWARE REMOVED)
Route::get('/orangtua/children', function () {
    return response()->json(['message' => 'Orangtua children endpoint - Authentication disabled']);
});

// Siswa routes - NOW PUBLIC (ROLE MIDDLEWARE REMOVED)
// Form submission is now available in the forms prefix above

// Fallback for undefined routes
Route::fallback(function () {
    return response()->json([
        'message' => 'Route not found.'
    ], 404);
});
