/**
 * Modern Interactive Components for Dashboard
 * Includes modals, notifications, tooltips, and other UI components
 */

class ModernComponents {
    constructor() {
        this.modals = new Map();
        this.notifications = [];
        this.tooltips = new Map();
        this.init();
    }

    init() {
        this.createNotificationContainer();
        this.initModals();
        this.initTooltips();
        this.bindGlobalEvents();
    }

    // Notification System
    createNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'fixed top-4 right-4 z-9999 space-y-3 pointer-events-none';
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info', duration = 5000, options = {}) {
        const id = 'notification-' + Date.now();
        const notification = document.createElement('div');
        
        const icons = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'alert-triangle',
            info: 'info'
        };

        const colors = {
            success: 'bg-green-500/90 border-green-400',
            error: 'bg-red-500/90 border-red-400',
            warning: 'bg-yellow-500/90 border-yellow-400',
            info: 'bg-blue-500/90 border-blue-400'
        };

        notification.id = id;
        notification.className = `
            notification transform translate-x-full transition-all duration-300 ease-out
            ${colors[type]} backdrop-filter backdrop-blur-lg
            border rounded-xl p-4 shadow-xl pointer-events-auto
            max-w-sm w-full
        `;

        notification.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="${icons[type]}" class="w-5 h-5 text-white"></i>
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium text-white">${message}</p>
                    ${options.description ? `<p class="text-xs text-white/80 mt-1">${options.description}</p>` : ''}
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="text-white/70 hover:text-white transition-colors" onclick="modernComponents.removeNotification('${id}')">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
            ${options.actions ? `
                <div class="mt-3 flex space-x-2">
                    ${options.actions.map(action => `
                        <button class="btn-ghost text-xs px-3 py-1 rounded-lg text-white hover:bg-white/20" onclick="${action.onClick}">
                            ${action.label}
                        </button>
                    `).join('')}
                </div>
            ` : ''}
        `;

        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        // Initialize Lucide icons for the notification
        lucide.createIcons();

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
            notification.classList.add('translate-x-0');
        }, 100);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(id);
            }, duration);
        }

        this.notifications.push({ id, element: notification });
        return id;
    }

    removeNotification(id) {
        const notification = document.getElementById(id);
        if (notification) {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                notification.remove();
                this.notifications = this.notifications.filter(n => n.id !== id);
            }, 300);
        }
    }

    // Modal System
    initModals() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target.dataset.modalId);
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal-overlay.show');
                if (openModal) {
                    this.closeModal(openModal.dataset.modalId);
                }
            }
        });
    }

    createModal(id, options = {}) {
        const modal = document.createElement('div');
        modal.id = id;
        modal.className = 'modal-overlay';
        modal.dataset.modalId = id;

        modal.innerHTML = `
            <div class="modal-content bg-slate-800 border border-slate-700 rounded-2xl shadow-2xl max-w-md w-full mx-4 transform scale-95 transition-transform duration-300">
                <div class="p-6">
                    ${options.title ? `
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-white">${options.title}</h3>
                            <button class="text-slate-400 hover:text-white transition-colors" onclick="modernComponents.closeModal('${id}')">
                                <i data-lucide="x" class="w-5 h-5"></i>
                            </button>
                        </div>
                    ` : ''}
                    <div class="modal-body">
                        ${options.content || ''}
                    </div>
                    ${options.actions ? `
                        <div class="flex justify-end space-x-3 mt-6">
                            ${options.actions.map(action => `
                                <button class="${action.class || 'btn-ghost'}" onclick="${action.onClick}">
                                    ${action.label}
                                </button>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.modals.set(id, modal);

        // Initialize Lucide icons for the modal
        lucide.createIcons();

        return modal;
    }

    showModal(id, options = {}) {
        let modal = this.modals.get(id);
        
        if (!modal && options) {
            modal = this.createModal(id, options);
        }

        if (modal) {
            modal.classList.add('show');
            const content = modal.querySelector('.modal-content');
            setTimeout(() => {
                content.classList.remove('scale-95');
                content.classList.add('scale-100');
            }, 100);
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(id) {
        const modal = this.modals.get(id);
        if (modal) {
            const content = modal.querySelector('.modal-content');
            content.classList.add('scale-95');
            content.classList.remove('scale-100');
            
            setTimeout(() => {
                modal.classList.remove('show');
                document.body.style.overflow = '';
            }, 200);
        }
    }

    // Tooltip System
    initTooltips() {
        document.addEventListener('mouseenter', (e) => {
            if (e.target.hasAttribute('data-tooltip')) {
                this.showTooltip(e.target);
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.hasAttribute('data-tooltip')) {
                this.hideTooltip(e.target);
            }
        }, true);
    }

    showTooltip(element) {
        const text = element.getAttribute('data-tooltip');
        const position = element.getAttribute('data-tooltip-position') || 'top';
        
        const tooltip = document.createElement('div');
        tooltip.className = `
            tooltip fixed z-50 px-3 py-2 text-xs font-medium text-white
            bg-slate-900 border border-slate-700 rounded-lg shadow-xl
            pointer-events-none opacity-0 transition-opacity duration-200
        `;
        tooltip.textContent = text;

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        let top, left;

        switch (position) {
            case 'bottom':
                top = rect.bottom + 8;
                left = rect.left + rect.width / 2 - tooltipRect.width / 2;
                break;
            case 'left':
                top = rect.top + rect.height / 2 - tooltipRect.height / 2;
                left = rect.left - tooltipRect.width - 8;
                break;
            case 'right':
                top = rect.top + rect.height / 2 - tooltipRect.height / 2;
                left = rect.right + 8;
                break;
            default: // top
                top = rect.top - tooltipRect.height - 8;
                left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        }

        tooltip.style.top = Math.max(8, top) + 'px';
        tooltip.style.left = Math.max(8, Math.min(window.innerWidth - tooltipRect.width - 8, left)) + 'px';

        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 50);

        this.tooltips.set(element, tooltip);
    }

    hideTooltip(element) {
        const tooltip = this.tooltips.get(element);
        if (tooltip) {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                tooltip.remove();
                this.tooltips.delete(element);
            }, 200);
        }
    }

    // Loading States
    showLoading(element, text = 'Loading...') {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (element) {
            element.innerHTML = `
                <div class="flex items-center justify-center p-8">
                    <div class="loading-spinner mr-3"></div>
                    <span class="text-sm text-white/70">${text}</span>
                </div>
            `;
        }
    }

    hideLoading(element, content) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (element) {
            element.innerHTML = content;
        }
    }

    // Global Events
    bindGlobalEvents() {
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
    }

    // Utility Methods
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    formatDate(date, options = {}) {
        return new Intl.DateTimeFormat('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            ...options
        }).format(new Date(date));
    }

    formatTime(date) {
        return new Intl.DateTimeFormat('id-ID', {
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }
}

// Initialize global instance
window.modernComponents = new ModernComponents();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernComponents;
}

