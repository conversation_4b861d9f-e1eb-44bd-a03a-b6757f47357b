<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuesioner SRQ-20 | Vibrant Edition</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* Glassmorphism effect for the main card */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Custom radio button styles */
        .custom-radio-label {
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        input[type="radio"]:checked + .custom-radio-label {
            border-color: #a78bfa; /* violet-400 */
            background-color: rgba(167, 139, 250, 0.2);
            color: #ffffff;
            font-weight: 700;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(167, 139, 250, 0.3);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes popIn {
            0% { opacity: 0; transform: scale(0.8); }
            80% { opacity: 1; transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out forwards;
        }
        .animate-pop-in {
            animation: popIn 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
        }
        .question-card {
            opacity: 0;
            animation: fadeIn 0.6s ease-out forwards;
        }
    </style>
</head>
<body class="text-slate-100 flex items-center justify-center min-h-screen p-4">

    <!-- Timer Display -->
    <div id="timer-container" class="fixed top-4 right-4 sm:top-6 sm:right-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-4 py-2 text-lg font-semibold shadow-lg z-10">
        <span id="timer">00:00</span>
    </div>

    <main class="glass-card w-full max-w-3xl rounded-2xl shadow-2xl p-6 sm:p-8 md:p-10 transition-all duration-300">
        
        <!-- Form Section -->
        <div id="form-section">
            <div class="text-center mb-8">
                <h1 class="text-3xl sm:text-4xl font-extrabold text-white">Kuesioner SRQ-20</h1>
                <p class="text-slate-300 mt-2 max-w-xl mx-auto">Skrining Kesehatan Jiwa Anda</p>
            </div>

            <!-- Instructions Section -->
            <div class="mb-8 p-4 bg-white/5 border-l-4 border-violet-400 rounded-r-lg">
                <h3 class="font-bold text-lg text-violet-300">Petunjuk</h3>
                <p class="mt-2 text-slate-200">
                    Jawablah pertanyaan berdasarkan apa yang Anda rasakan selama <strong>30 hari terakhir</strong>.
                </p>
            </div>

            <div id="alert-message" class="hidden bg-red-400/50 border border-red-400 text-white px-4 py-3 rounded-lg relative mb-6 animate-fade-in" role="alert">
                <strong class="font-bold">Perhatian!</strong>
                <span class="block sm:inline"> Harap jawab semua pertanyaan.</span>
            </div>

            <form id="srqForm">
                <div id="questions-container" class="space-y-6">
                    <!-- Questions will be dynamically inserted here -->
                </div>

                <div class="mt-10 text-center">
                    <button type="submit" class="w-full sm:w-auto bg-gradient-to-r from-violet-500 to-fuchsia-500 hover:from-violet-600 hover:to-fuchsia-600 text-white font-bold py-3 px-10 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-violet-300">
                        Selesai & Lihat Hasil
                    </button>
                </div>
            </form>
        </div>

        <!-- Result Section -->
        <div id="result-section" class="hidden text-center">
            <div id="result-content" class="p-6 rounded-lg mb-6 animate-pop-in">
                <h2 class="text-3xl font-bold mb-4 text-white">Hasil Anda</h2>
                <p class="text-lg text-slate-300">Total Skor "Ya":</p>
                <p id="score" class="text-7xl font-extrabold my-2"></p>
                <p id="interpretation" class="text-xl font-semibold"></p>
                <p id="recommendation" class="text-slate-300 mt-4 max-w-md mx-auto"></p>
                <div class="mt-6 pt-4 border-t border-white/20 text-slate-200">
                    <p>Total Waktu Pengerjaan: <strong id="total-time" class="font-bold"></strong></p>
                </div>
            </div>
            
            <button id="reset-button" class="w-full sm:w-auto bg-white/20 hover:bg-white/30 text-white font-bold py-3 px-8 rounded-lg shadow-lg transition-transform transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/50">
                Ulangi Kuesioner
            </button>
        </div>

    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Element selectors
            const srqForm = document.getElementById('srqForm');
            const questionsContainer = document.getElementById('questions-container');
            const formSection = document.getElementById('form-section');
            const resultSection = document.getElementById('result-section');
            const scoreEl = document.getElementById('score');
            const interpretationEl = document.getElementById('interpretation');
            const recommendationEl = document.getElementById('recommendation');
            const resultContentEl = document.getElementById('result-content');
            const resetButton = document.getElementById('reset-button');
            const alertMessage = document.getElementById('alert-message');
            const timerEl = document.getElementById('timer');
            const totalTimeEl = document.getElementById('total-time');

            // State variables
            let timerInterval;
            let totalSeconds = 0;
            let lastAnswerTimestamp = 0;
            let answerTimes = [];

            const questions = [
                "Apakah Anda sering merasa sakit kepala?", "Apakah nafsu makan Anda buruk?", "Apakah Anda tidur tidak nyenyak?", "Apakah Anda mudah merasa takut?", "Apakah Anda merasa cemas, tegang, atau khawatir?", "Apakah tangan Anda gemetar?", "Apakah pencernaan Anda terganggu atau buruk?", "Apakah Anda sulit berpikir jernih?", "Apakah Anda merasa tidak bahagia?", "Apakah Anda lebih sering menangis?", "Apakah Anda merasa sulit untuk menikmati kegiatan sehari-hari?", "Apakah Anda mengalami kesulitan dalam mengambil keputusan?", "Apakah pekerjaan sehari-hari Anda terganggu?", "Apakah Anda tidak mampu melakukan hal-hal yang bermanfaat dalam hidup?", "Apakah Anda kehilangan minat pada berbagai hal?", "Apakah Anda merasa tidak berharga?", "Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?", "Apakah Anda merasa lelah sepanjang waktu?", "Apakah Anda merasa tidak enak di perut?", "Apakah Anda mudah lelah?"
            ];

            function startTimer() {
                lastAnswerTimestamp = Date.now();
                timerInterval = setInterval(() => {
                    totalSeconds++;
                    timerEl.textContent = formatTime(totalSeconds);
                }, 1000);
            }

            function formatTime(seconds) {
                const min = Math.floor(seconds / 60).toString().padStart(2, '0');
                const sec = (seconds % 60).toString().padStart(2, '0');
                return `${min}:${sec}`;
            }

            function renderQuestions() {
                let questionsHTML = '';
                questions.forEach((q, index) => {
                    const questionNumber = index + 1;
                    questionsHTML += `
                        <div class="question-card p-4 border border-white/20 rounded-lg" style="animation-delay: ${index * 100}ms;">
                            <p class="text-slate-200 mb-3 font-medium">${questionNumber}. ${q}</p>
                            <div class="flex items-center justify-end space-x-4">
                                <input type="radio" id="q${questionNumber}-yes" name="q${questionNumber}" value="1" class="sr-only" required>
                                <label for="q${questionNumber}-yes" class="custom-radio-label cursor-pointer w-24 text-center py-2 px-4 border-2 border-white/30 rounded-lg">Ya</label>
                                
                                <input type="radio" id="q${questionNumber}-no" name="q${questionNumber}" value="0" class="sr-only" required>
                                <label for="q${questionNumber}-no" class="custom-radio-label cursor-pointer w-24 text-center py-2 px-4 border-2 border-white/30 rounded-lg">Tidak</label>
                            </div>
                        </div>
                    `;
                });
                questionsContainer.innerHTML = questionsHTML;
            }
            
            function recordAnswerTime() {
                const now = Date.now();
                const timeTaken = (now - lastAnswerTimestamp) / 1000; // in seconds
                answerTimes.push(timeTaken);
                lastAnswerTimestamp = now;
            }

            questionsContainer.addEventListener('change', (e) => {
                if (e.target.type === 'radio') {
                    recordAnswerTime();
                }
            });

            srqForm.addEventListener('submit', (e) => {
                e.preventDefault();
                
                const formData = new FormData(srqForm);
                const answeredQuestions = Array.from(formData.keys()).length;

                if (answeredQuestions < questions.length) {
                    alertMessage.classList.remove('hidden');
                    window.scrollTo(0, 0);
                    return;
                }
                
                alertMessage.classList.add('hidden');
                clearInterval(timerInterval);

                let score = 0;
                for (let value of formData.values()) {
                    score += parseInt(value);
                }

                displayResult(score);
            });

            function displayResult(score) {
                scoreEl.textContent = score;
                totalTimeEl.textContent = formatTime(totalSeconds);

                resultContentEl.classList.remove('bg-green-500/20', 'bg-yellow-500/20');
                scoreEl.classList.remove('text-green-300', 'text-yellow-300');
                interpretationEl.classList.remove('text-green-200', 'text-yellow-200');

                if (score > 6) {
                    resultContentEl.classList.add('bg-yellow-500/20');
                    scoreEl.classList.add('text-yellow-300');
                    interpretationEl.classList.add('text-yellow-200');
                    interpretationEl.textContent = "Terindikasi Mengalami Gangguan Emosional";
                    recommendationEl.textContent = "Skor Anda menunjukkan adanya kemungkinan masalah kesehatan jiwa. Disarankan untuk berkonsultasi dengan profesional.";
                } else {
                    resultContentEl.classList.add('bg-green-500/20');
                    scoreEl.classList.add('text-green-300');
                    interpretationEl.classList.add('text-green-200');
                    interpretationEl.textContent = "Kondisi Emosional Anda Cukup Baik";
                    recommendationEl.textContent = "Skor Anda berada dalam rentang normal. Terus pertahankan gaya hidup sehat dan kelola stres dengan baik.";
                }

                formSection.classList.add('hidden');
                resultSection.classList.remove('hidden');
            }

            function resetApp() {
                srqForm.reset();
                resultSection.classList.add('hidden');
                formSection.classList.remove('hidden');
                
                clearInterval(timerInterval);
                totalSeconds = 0;
                answerTimes = [];
                timerEl.textContent = '00:00';
                
                renderQuestions();
                startTimer();
                window.scrollTo(0, 0);
            }

            resetButton.addEventListener('click', resetApp);
            
            // Initial setup
            renderQuestions();
            startTimer();
        });
    </script>
</body>
</html>
