<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\StudentParentRelationship;
use App\Models\User;

class StudentParentRelationshipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get parent user
        $parent = User::where('email', '<EMAIL>')->first();

        // Get student users
        $student1 = User::where('email', '<EMAIL>')->first();
        $student2 = User::where('email', '<EMAIL>')->first();
        $demoStudent = User::where('email', '<EMAIL>')->first();

        if ($parent && $student1) {
            StudentParentRelationship::updateOrCreate(
                ['student_id' => $student1->id, 'parent_id' => $parent->id],
                [
                    'relationship_type' => 'ayah',
                    'is_primary' => true,
                    'can_view_results' => true
                ]
            );
        }

        if ($parent && $student2) {
            StudentParentRelationship::updateOrCreate(
                ['student_id' => $student2->id, 'parent_id' => $parent->id],
                [
                    'relationship_type' => 'ayah',
                    'is_primary' => false,
                    'can_view_results' => true
                ]
            );
        }

        if ($parent && $demoStudent) {
            StudentParentRelationship::updateOrCreate(
                ['student_id' => $demoStudent->id, 'parent_id' => $parent->id],
                [
                    'relationship_type' => 'wali',
                    'is_primary' => false,
                    'can_view_results' => true
                ]
            );
        }
    }
}
