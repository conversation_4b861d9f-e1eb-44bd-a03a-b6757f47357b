<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_parent_relationships', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('parent_id')->constrained('users')->onDelete('cascade');
            $table->enum('relationship_type', ['ayah', 'ibu', 'wali'])->default('ayah');
            $table->boolean('is_primary')->default(false); // Primary contact
            $table->boolean('can_view_results')->default(true); // Can view assessment results
            $table->timestamps();

            // Ensure unique student-parent relationship
            $table->unique(['student_id', 'parent_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_parent_relationships');
    }
};
