/**
 * Performance Optimization Utilities for Modern Dashboard
 * Includes lazy loading, intersection observers, and animation optimizations
 */

class PerformanceOptimizer {
    constructor() {
        this.observers = new Map();
        this.animationQueue = [];
        this.isAnimating = false;
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupLazyLoading();
        this.optimizeAnimations();
        this.preloadCriticalResources();
        this.setupPerformanceMonitoring();
    }

    // Intersection Observer for animations
    setupIntersectionObserver() {
        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerAnimation(entry.target);
                    animationObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        this.observers.set('animation', animationObserver);

        // Observe elements with animation classes
        document.querySelectorAll('[class*="animate-"]').forEach(el => {
            animationObserver.observe(el);
        });
    }

    // Lazy loading for images and charts
    setupLazyLoading() {
        const lazyObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadElement(entry.target);
                    lazyObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '100px'
        });

        this.observers.set('lazy', lazyObserver);

        // Observe lazy elements
        document.querySelectorAll('[data-lazy]').forEach(el => {
            lazyObserver.observe(el);
        });
    }

    // Optimize animations with RAF
    optimizeAnimations() {
        // Use requestAnimationFrame for smooth animations
        this.animationFrame = (callback) => {
            return requestAnimationFrame(callback);
        };

        // Batch DOM reads and writes
        this.batchDOMOperations = (operations) => {
            this.animationFrame(() => {
                // Read phase
                const reads = operations.filter(op => op.type === 'read');
                const readResults = reads.map(op => op.fn());

                this.animationFrame(() => {
                    // Write phase
                    const writes = operations.filter(op => op.type === 'write');
                    writes.forEach((op, index) => {
                        op.fn(readResults[index]);
                    });
                });
            });
        };
    }

    // Preload critical resources
    preloadCriticalResources() {
        const criticalResources = [
            { href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap', as: 'style' },
            { href: 'https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js', as: 'script' }
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            if (resource.as === 'style') {
                link.onload = () => {
                    link.rel = 'stylesheet';
                };
            }
            document.head.appendChild(link);
        });
    }

    // Performance monitoring
    setupPerformanceMonitoring() {
        // Monitor Core Web Vitals
        if ('web-vital' in window) {
            this.monitorWebVitals();
        }

        // Monitor memory usage
        if (performance.memory) {
            this.monitorMemoryUsage();
        }

        // Monitor frame rate
        this.monitorFrameRate();
    }

    // Trigger animations with stagger effect
    triggerAnimation(element) {
        const animationClass = Array.from(element.classList)
            .find(cls => cls.startsWith('animate-'));

        if (animationClass) {
            element.style.opacity = '0';
            element.style.transform = this.getInitialTransform(animationClass);

            this.animationFrame(() => {
                element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
                element.style.opacity = '1';
                element.style.transform = 'none';
            });
        }
    }

    getInitialTransform(animationClass) {
        const transforms = {
            'animate-fade-in-up': 'translateY(30px)',
            'animate-fade-in-left': 'translateX(-30px)',
            'animate-fade-in-right': 'translateX(30px)',
            'animate-scale-in': 'scale(0.9)'
        };
        return transforms[animationClass] || 'translateY(20px)';
    }

    // Load lazy elements
    loadElement(element) {
        const lazyType = element.dataset.lazy;

        switch (lazyType) {
            case 'chart':
                this.loadChart(element);
                break;
            case 'image':
                this.loadImage(element);
                break;
            case 'component':
                this.loadComponent(element);
                break;
        }
    }

    loadChart(element) {
        // Show loading state
        element.innerHTML = `
            <div class="flex items-center justify-center h-64">
                <div class="loading-spinner mr-3"></div>
                <span class="text-sm text-white/70">Loading chart...</span>
            </div>
        `;

        // Simulate chart loading
        setTimeout(() => {
            element.innerHTML = '<canvas id="' + element.dataset.chartId + '"></canvas>';
            // Trigger chart initialization
            if (window.initChart) {
                window.initChart(element.dataset.chartId);
            }
        }, 500);
    }

    loadImage(element) {
        const img = new Image();
        img.onload = () => {
            element.src = img.src;
            element.classList.add('loaded');
        };
        img.src = element.dataset.src;
    }

    loadComponent(element) {
        const componentName = element.dataset.component;
        // Load component dynamically
        import(`./components/${componentName}.js`)
            .then(module => {
                const Component = module.default;
                new Component(element);
            })
            .catch(err => {
                console.warn(`Failed to load component: ${componentName}`, err);
            });
    }

    // Debounce utility
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Throttle utility
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Monitor Web Vitals
    monitorWebVitals() {
        // This would integrate with web-vitals library
        console.log('Web Vitals monitoring initialized');
    }

    // Monitor memory usage
    monitorMemoryUsage() {
        const checkMemory = () => {
            const memory = performance.memory;
            const usage = {
                used: Math.round(memory.usedJSHeapSize / 1048576),
                total: Math.round(memory.totalJSHeapSize / 1048576),
                limit: Math.round(memory.jsHeapSizeLimit / 1048576)
            };

            if (usage.used / usage.limit > 0.8) {
                console.warn('High memory usage detected:', usage);
                this.optimizeMemory();
            }
        };

        setInterval(checkMemory, 30000); // Check every 30 seconds
    }

    // Monitor frame rate
    monitorFrameRate() {
        let lastTime = performance.now();
        let frames = 0;

        const measureFPS = (currentTime) => {
            frames++;
            if (currentTime >= lastTime + 1000) {
                const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                if (fps < 30) {
                    console.warn('Low FPS detected:', fps);
                    this.optimizePerformance();
                }
                frames = 0;
                lastTime = currentTime;
            }
            requestAnimationFrame(measureFPS);
        };

        requestAnimationFrame(measureFPS);
    }

    // Optimize memory usage
    optimizeMemory() {
        // Clear unused observers
        this.observers.forEach((observer, key) => {
            if (key !== 'animation' && key !== 'lazy') {
                observer.disconnect();
                this.observers.delete(key);
            }
        });

        // Clear animation queue
        this.animationQueue = [];

        // Suggest garbage collection (if available)
        if (window.gc) {
            window.gc();
        }
    }

    // Optimize performance
    optimizePerformance() {
        // Reduce animation complexity
        document.documentElement.style.setProperty('--transition-fast', '100ms');
        document.documentElement.style.setProperty('--transition-normal', '200ms');

        // Disable non-critical animations
        document.querySelectorAll('.animate-pulse').forEach(el => {
            el.classList.remove('animate-pulse');
        });
    }

    // Cleanup
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.animationQueue = [];
    }
}

// Initialize performance optimizer
window.performanceOptimizer = new PerformanceOptimizer();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}
