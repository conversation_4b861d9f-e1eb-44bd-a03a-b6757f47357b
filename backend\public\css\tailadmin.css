/* TailAdmin CSS Framework for SantriMental */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Variables - TailAdmin Color Scheme */
:root {
  /* Primary Colors */
  --primary: #3c50e0;
  --primary-dark: #2e3fd4;
  --primary-light: #5a6cf0;
  
  /* Secondary Colors */
  --secondary: #80caee;
  --secondary-dark: #6bb8e0;
  --secondary-light: #9dd5f2;
  
  /* Success Colors */
  --success: #10b981;
  --success-dark: #059669;
  --success-light: #34d399;
  
  /* Warning Colors */
  --warning: #f59e0b;
  --warning-dark: #d97706;
  --warning-light: #fbbf24;
  
  /* Danger Colors */
  --danger: #f56565;
  --danger-dark: #e53e3e;
  --danger-light: #fc8181;
  
  /* Gray Colors */
  --gray-1: #f7f9fc;
  --gray-2: #e2e8f0;
  --gray-3: #cbd5e0;
  --gray-4: #a0aec0;
  --gray-5: #718096;
  --gray-6: #4a5568;
  --gray-7: #2d3748;
  --gray-8: #1a202c;
  --gray-9: #171923;
  
  /* Dark Mode Colors */
  --dark-1: #1c2434;
  --dark-2: #24303f;
  --dark-3: #313d4a;
  --dark-4: #3c4858;
  --dark-5: #475569;
  --dark-6: #64748b;
  --dark-7: #94a3b8;
  
  /* Background Colors */
  --body-bg: #f1f5f9;
  --body-bg-dark: #0f172a;
  --card-bg: #ffffff;
  --card-bg-dark: #1e293b;
  
  /* Text Colors */
  --text-primary: #1e293b;
  --text-primary-dark: #f8fafc;
  --text-secondary: #64748b;
  --text-secondary-dark: #94a3b8;
  
  /* Border Colors */
  --border-color: #e2e8f0;
  --border-color-dark: #334155;
  
  /* Shadow */
  --shadow-default: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-dropdown: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--body-bg);
  color: var(--text-primary);
  line-height: 1.6;
  transition: all 0.3s ease;
}

body.dark {
  background-color: var(--body-bg-dark);
  color: var(--text-primary-dark);
}

/* Layout Components */
.tailadmin-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Sidebar Styles */
.tailadmin-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 280px;
  background: var(--card-bg);
  border-right: 1px solid var(--border-color);
  z-index: 999;
  transition: all 0.3s ease;
  overflow-y: auto;
}

.dark .tailadmin-sidebar {
  background: var(--card-bg-dark);
  border-right-color: var(--border-color-dark);
}

.tailadmin-sidebar.collapsed {
  width: 80px;
}

.tailadmin-sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.dark .tailadmin-sidebar-header {
  border-bottom-color: var(--border-color-dark);
}

.tailadmin-sidebar-nav {
  padding: 1rem 0;
}

.tailadmin-nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.tailadmin-nav-item:hover {
  background-color: var(--gray-1);
  color: var(--text-primary);
}

.dark .tailadmin-nav-item:hover {
  background-color: var(--dark-2);
  color: var(--text-primary-dark);
}

.tailadmin-nav-item.active {
  background-color: var(--primary);
  color: white;
  border-left-color: var(--primary-dark);
}

.tailadmin-nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Header Styles */
.tailadmin-header {
  position: sticky;
  top: 0;
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
  margin-left: 280px;
  z-index: 998;
  transition: all 0.3s ease;
}

.dark .tailadmin-header {
  background: var(--card-bg-dark);
  border-bottom-color: var(--border-color-dark);
}

.tailadmin-header.sidebar-collapsed {
  margin-left: 80px;
}

/* Main Content */
.tailadmin-main {
  margin-left: 280px;
  min-height: 100vh;
  padding: 2rem;
  transition: all 0.3s ease;
}

.tailadmin-main.sidebar-collapsed {
  margin-left: 80px;
}

/* Card Components */
.tailadmin-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
}

.dark .tailadmin-card {
  background: var(--card-bg-dark);
  border-color: var(--border-color-dark);
}

.tailadmin-card:hover {
  box-shadow: var(--shadow-dropdown);
  transform: translateY(-2px);
}

.tailadmin-card-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.dark .tailadmin-card-header {
  border-bottom-color: var(--border-color-dark);
}

.tailadmin-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.dark .tailadmin-card-title {
  color: var(--text-primary-dark);
}

/* Button Components */
.tailadmin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  gap: 0.5rem;
}

.tailadmin-btn-primary {
  background-color: var(--primary);
  color: white;
}

.tailadmin-btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.tailadmin-btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.tailadmin-btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.tailadmin-btn-success {
  background-color: var(--success);
  color: white;
}

.tailadmin-btn-success:hover {
  background-color: var(--success-dark);
}

.tailadmin-btn-warning {
  background-color: var(--warning);
  color: white;
}

.tailadmin-btn-warning:hover {
  background-color: var(--warning-dark);
}

.tailadmin-btn-danger {
  background-color: var(--danger);
  color: white;
}

.tailadmin-btn-danger:hover {
  background-color: var(--danger-dark);
}

.tailadmin-btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.dark .tailadmin-btn-outline {
  border-color: var(--border-color-dark);
  color: var(--text-primary-dark);
}

.tailadmin-btn-outline:hover {
  background-color: var(--gray-1);
}

.dark .tailadmin-btn-outline:hover {
  background-color: var(--dark-2);
}

/* Stats Card */
.tailadmin-stats-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
}

.dark .tailadmin-stats-card {
  background: var(--card-bg-dark);
  border-color: var(--border-color-dark);
}

.tailadmin-stats-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.tailadmin-stats-icon.primary {
  background-color: rgba(60, 80, 224, 0.1);
  color: var(--primary);
}

.tailadmin-stats-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.tailadmin-stats-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.tailadmin-stats-icon.danger {
  background-color: rgba(245, 101, 101, 0.1);
  color: var(--danger);
}

.tailadmin-stats-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.dark .tailadmin-stats-value {
  color: var(--text-primary-dark);
}

.tailadmin-stats-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.dark .tailadmin-stats-label {
  color: var(--text-secondary-dark);
}

/* Badge Components */
.tailadmin-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.tailadmin-badge.primary {
  background-color: rgba(60, 80, 224, 0.1);
  color: var(--primary);
}

.tailadmin-badge.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.tailadmin-badge.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.tailadmin-badge.danger {
  background-color: rgba(245, 101, 101, 0.1);
  color: var(--danger);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tailadmin-sidebar {
    transform: translateX(-100%);
  }
  
  .tailadmin-sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .tailadmin-header,
  .tailadmin-main {
    margin-left: 0;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tailadmin-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Utilities */
.tailadmin-text-primary {
  color: var(--text-primary);
}

.dark .tailadmin-text-primary {
  color: var(--text-primary-dark);
}

.tailadmin-text-secondary {
  color: var(--text-secondary);
}

.dark .tailadmin-text-secondary {
  color: var(--text-secondary-dark);
}

.tailadmin-bg-primary {
  background-color: var(--primary);
}

.tailadmin-bg-success {
  background-color: var(--success);
}

.tailadmin-bg-warning {
  background-color: var(--warning);
}

.tailadmin-bg-danger {
  background-color: var(--danger);
}
