<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Assessment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'total_score',
        'completion_time',
        'recommendation',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total_score' => 'integer',
        'completion_time' => 'integer',
    ];

    /**
     * Get the user that owns the assessment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the answers for the assessment.
     */
    public function answers(): HasMany
    {
        return $this->hasMany(AssessmentAnswer::class);
    }

    /**
     * Calculate status based on total score.
     *
     * @return string
     */
    public function calculateStatus(): string
    {
        return $this->total_score > 6 ? 'concern' : 'normal';
    }

    /**
     * Generate recommendation based on assessment results.
     *
     * @return string
     */
    public function generateRecommendation(): string
    {
        if ($this->status === 'normal') {
            return 'Berdasarkan hasil tes, kondisi kesehatan mental Anda dalam keadaan baik. ' .
                   'Tetap jaga pola hidup sehat dan kelola stres dengan baik.';
        }

        return 'Hasil tes menunjukkan adanya indikasi gangguan kesehatan mental. ' .
               'Disarankan untuk berkonsultasi dengan profesional kesehatan mental ' .
               'seperti psikolog atau psikiater untuk evaluasi lebih lanjut.';
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Set status before saving
        static::saving(function ($assessment) {
            if (!$assessment->status) {
                $assessment->status = $assessment->calculateStatus();
            }
        });

        // Generate recommendation if not provided
        static::creating(function ($assessment) {
            if (!$assessment->recommendation) {
                $assessment->recommendation = $assessment->generateRecommendation();
            }
        });
    }

    /**
     * Scope a query to only include assessments from current month.
     */
    public function scopeCurrentMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * Get assessment trend data.
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTrend($limit = 10)
    {
        return static::select('id', 'total_score', 'status', 'created_at')
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get monthly distribution data.
     *
     * @return array
     */
    public static function getMonthlyDistribution()
    {
        return static::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month')
            ->selectRaw('COUNT(*) as total')
            ->selectRaw('COUNT(CASE WHEN status = "concern" THEN 1 END) as concerns')
            ->selectRaw('AVG(total_score) as average_score')
            ->groupBy('month')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();
    }
}
