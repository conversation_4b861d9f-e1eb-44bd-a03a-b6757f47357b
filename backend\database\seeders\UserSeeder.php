<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Admin',
                'last_name' => 'System',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
                'role_id' => 1, // Admin role
                'phone' => '081234567890',
                'is_active' => true
            ]
        );

        // Create Guru user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Ahmad',
                'last_name' => 'Hidayat',
                'email' => '<EMAIL>',
                'password' => Hash::make('guru123'),
                'email_verified_at' => now(),
                'role_id' => 2, // <PERSON> role
                'teacher_id' => 'GR001',
                'phone' => '081234567891',
                'is_active' => true
            ]
        );

        // Create Orangtua user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Budi',
                'last_name' => 'Santoso',
                'email' => '<EMAIL>',
                'password' => Hash::make('orangtua123'),
                'email_verified_at' => now(),
                'role_id' => 3, // Orangtua role
                'phone' => '081234567892',
                'address' => 'Jl. Pesantren No. 123, Jakarta',
                'is_active' => true
            ]
        );

        // Create Siswa users
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Muhammad',
                'last_name' => 'Rizki',
                'email' => '<EMAIL>',
                'password' => Hash::make('siswa123'),
                'email_verified_at' => now(),
                'role_id' => 4, // Siswa role
                'student_id' => 'SW001',
                'class' => '7A',
                'grade' => '7',
                'gender' => 'L',
                'birth_date' => '2010-05-15',
                'phone' => '081234567893',
                'is_active' => true
            ]
        );

        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Fatimah',
                'last_name' => 'Zahra',
                'email' => '<EMAIL>',
                'password' => Hash::make('siswa123'),
                'email_verified_at' => now(),
                'role_id' => 4, // Siswa role
                'student_id' => 'SW002',
                'class' => '8B',
                'grade' => '8',
                'gender' => 'P',
                'birth_date' => '2009-08-22',
                'phone' => '081234567894',
                'is_active' => true
            ]
        );

        // Create demo user (siswa)
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Demo',
                'last_name' => 'Siswa',
                'email' => '<EMAIL>',
                'password' => Hash::make('demo123'),
                'email_verified_at' => now(),
                'role_id' => 4, // Siswa role
                'student_id' => 'SW999',
                'class' => '9A',
                'grade' => '9',
                'gender' => 'L',
                'birth_date' => '2008-12-01',
                'is_active' => true
            ]
        );
    }
}
