<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('total_score');
            $table->integer('completion_time')->comment('Time taken to complete the assessment in seconds');
            $table->text('recommendation')->nullable();
            $table->enum('status', ['normal', 'concern'])->default('normal')
                  ->comment('normal: score <= 6, concern: score > 6');
            $table->timestamps();
            
            // Add index for faster queries
            $table->index(['user_id', 'created_at']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessments');
    }
};
