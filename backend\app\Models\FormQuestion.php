<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'form_template_id',
        'question_number',
        'question_text',
        'question_type',
        'options',
        'is_required',
        'score_weight'
    ];

    protected $casts = [
        'options' => 'array',
        'is_required' => 'boolean'
    ];

    public function formTemplate()
    {
        return $this->belongsTo(FormTemplate::class);
    }
}
