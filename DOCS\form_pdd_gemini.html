<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuesioner <PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Custom styles for smooth transitions and better UX */
        .card-enter {
            opacity: 0;
            transform: translateY(20px);
        }
        .card-enter-active {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 300ms, transform 300ms;
        }
        .gauge-bg {
            fill: none;
            stroke: #e2e8f0; /* slate-200 */
        }
        .gauge-fg {
            fill: none;
            stroke: #0ea5e9; /* sky-500 */
            stroke-linecap: round;
            transition: stroke-dashoffset 0.5s ease-out;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800 flex flex-col items-center justify-between min-h-screen p-4 sm:p-6 lg:p-8">

    <main id="app" class="w-full max-w-2xl mx-auto">

        <!-- Welcome Screen -->
        <div id="welcome-screen" class="text-center bg-white p-8 rounded-2xl shadow-lg">
            <div class="mx-auto w-16 h-16 mb-6 bg-sky-100 text-sky-600 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <h1 class="text-2xl sm:text-3xl font-bold text-slate-900 mb-4">Memahami Stigma Kesehatan Jiwa</h1>
            <p class="text-slate-600 mb-8 max-w-prose mx-auto">
                Selamat datang! Kuesioner ini dirancang untuk membantu Anda merefleksikan pandangan masyarakat terhadap kesehatan jiwa. Jawaban Anda sepenuhnya anonim dan akan membantu kita bersama membangun lingkungan yang lebih suportif.
            </p>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-left mb-10">
                <div class="bg-slate-100 p-4 rounded-lg">
                    <h3 class="font-semibold text-slate-800">12 Pertanyaan Singkat</h3>
                    <p class="text-sm text-slate-500">Hanya butuh ~3 menit.</p>
                </div>
                <div class="bg-slate-100 p-4 rounded-lg">
                    <h3 class="font-semibold text-slate-800">Sepenuhnya Anonim</h3>
                    <p class="text-sm text-slate-500">Kami tidak menyimpan data pribadi Anda.</p>
                </div>
                <div class="bg-slate-100 p-4 rounded-lg">
                    <h3 class="font-semibold text-slate-800">Hasil Instan</h3>
                    <p class="text-sm text-slate-500">Lihat interpretasi skor Anda di akhir.</p>
                </div>
            </div>
            <button id="start-btn" class="w-full sm:w-auto bg-sky-600 text-white font-bold py-3 px-10 rounded-lg shadow-md hover:bg-sky-700 focus:outline-none focus:ring-4 focus:ring-sky-300 transition-all duration-300">
                Mulai Kuesioner
            </button>
        </div>

        <!-- Questionnaire Screen -->
        <div id="question-screen" class="hidden">
            <!-- Progress Bar -->
            <div class="mb-6">
                <div class="flex justify-between mb-1">
                    <span id="progress-text" class="text-sm font-medium text-slate-600">Pertanyaan 1 dari 12</span>
                </div>
                <div class="w-full bg-slate-200 rounded-full h-2.5">
                    <div id="progress-bar" class="bg-sky-500 h-2.5 rounded-full transition-all duration-500" style="width: 8%"></div>
                </div>
            </div>

            <!-- Question Card -->
            <div id="question-card" class="bg-white p-8 rounded-2xl shadow-lg min-h-[350px] flex flex-col justify-center">
                <p id="question-text" class="text-center text-xl sm:text-2xl font-medium text-slate-800 mb-10"></p>
                <div id="answer-buttons" class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <!-- Answer buttons will be generated here by JS -->
                </div>
            </div>
        </div>

        <!-- Result Screen -->
        <div id="result-screen" class="hidden text-center bg-white p-8 rounded-2xl shadow-lg">
            <h2 class="text-2xl sm:text-3xl font-bold text-slate-900 mb-6">Hasil Refleksi Anda</h2>
            
            <!-- Gauge Chart -->
            <div class="relative w-48 h-48 mx-auto mb-4">
                <svg class="w-full h-full" viewBox="0 0 36 36">
                    <path class="gauge-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" stroke-width="4"></path>
                    <path id="gauge-fg" class="gauge-fg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" stroke-width="4" stroke-dasharray="100, 100" stroke-dashoffset="100"></path>
                </svg>
                <div class="absolute inset-0 flex flex-col items-center justify-center">
                    <span id="score-value" class="text-4xl font-bold text-sky-600">0</span>
                    <span class="text-sm text-slate-500">dari 36</span>
                </div>
            </div>

            <h3 id="stigma-level" class="text-xl font-semibold mb-2"></h3>
            <p id="stigma-interpretation" class="text-slate-600 mb-8 max-w-prose mx-auto"></p>

            <div class="bg-slate-100 p-6 rounded-lg text-left mb-8">
                <h4 class="font-bold text-lg text-slate-800 mb-3">Langkah Selanjutnya</h4>
                <p class="text-slate-600 mb-4">Kesadaran adalah langkah pertama. Jika Anda ingin belajar lebih banyak atau butuh dukungan, sumber daya berikut bisa membantu:</p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="https://www.who.int/news-room/fact-sheets/detail/mental-health-strengthening-our-response" target="_blank" class="flex-1 text-center bg-white border border-slate-300 text-slate-700 font-semibold py-2 px-4 rounded-lg hover:bg-slate-50 transition-colors">Pelajari Tentang Stigma</a>
                    <a href="https://www.pds-kji.org/home" target="_blank" class="flex-1 text-center bg-white border border-slate-300 text-slate-700 font-semibold py-2 px-4 rounded-lg hover:bg-slate-50 transition-colors">Cari Bantuan Profesional</a>
                </div>
            </div>

            <button id="restart-btn" class="w-full sm:w-auto bg-sky-600 text-white font-bold py-3 px-10 rounded-lg shadow-md hover:bg-sky-700 focus:outline-none focus:ring-4 focus:ring-sky-300 transition-all duration-300">
                Ulangi Kuesioner
            </button>
        </div>
    </main>

    <footer class="w-full max-w-2xl mx-auto text-center text-xs text-slate-400 mt-8">
        <p>Kuesioner diadaptasi dari Perceived-Devaluation Discrimination (Link, 1987) via Maya, N. (2021).</p>
        <p>Gadjah Mada Journal of Psychology (GamaJoP), 7(1), 22.</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- DATA ---
            const questions = [
                { text: "Kebanyakan orang percaya bahwa penderita gangguan mental berbahaya." },
                { text: "Orang dengan gangguan mental cenderung tidak bisa dipercaya." },
                { text: "Saya pikir masyarakat umumnya menganggap penderita gangguan mental lemah." },
                { text: "Seseorang akan dipandang negatif jika diketahui pernah ke psikolog." },
                { text: "Masyarakat akan menghindari orang dengan gangguan mental." },
                { text: "Kebanyakan orang menilai gangguan mental sebagai aib keluarga." },
                { text: "Penderita gangguan mental seringkali tidak diberi kesempatan bekerja." },
                { text: "Masyarakat lebih memilih tidak berteman dengan orang yang pernah dirawat karena gangguan mental." },
                { text: "Orang akan ragu terhadap kemampuan profesional penderita gangguan mental." },
                { text: "Banyak orang takut jika tinggal dekat dengan penderita gangguan mental." },
                { text: "Saya yakin masyarakat menganggap gangguan mental adalah kesalahan pribadi." },
                { text: "Masyarakat cenderung tidak percaya pada orang yang terbuka soal gangguan mentalnya." }
            ];

            const answers = [
                { text: "Sangat Setuju", score: 3 },
                { text: "Setuju", score: 2 },
                { text: "Tidak Setuju", score: 1 },
                { text: "Sangat Tidak Setuju", score: 0 }
            ];

            // --- STATE ---
            let currentQuestionIndex = 0;
            let userScore = 0;

            // --- DOM ELEMENTS ---
            const welcomeScreen = document.getElementById('welcome-screen');
            const questionScreen = document.getElementById('question-screen');
            const resultScreen = document.getElementById('result-screen');
            const startBtn = document.getElementById('start-btn');
            const restartBtn = document.getElementById('restart-btn');
            
            const progressText = document.getElementById('progress-text');
            const progressBar = document.getElementById('progress-bar');
            const questionCard = document.getElementById('question-card');
            const questionText = document.getElementById('question-text');
            const answerButtonsContainer = document.getElementById('answer-buttons');

            const scoreValue = document.getElementById('score-value');
            const gaugeFg = document.getElementById('gauge-fg');
            const stigmaLevel = document.getElementById('stigma-level');
            const stigmaInterpretation = document.getElementById('stigma-interpretation');

            // --- FUNCTIONS ---
            function startQuiz() {
                currentQuestionIndex = 0;
                userScore = 0;
                welcomeScreen.classList.add('hidden');
                resultScreen.classList.add('hidden');
                questionScreen.classList.remove('hidden');
                displayQuestion();
            }

            function displayQuestion() {
                // Add animation class
                questionCard.classList.remove('card-enter-active');
                questionCard.classList.add('card-enter');
                
                setTimeout(() => {
                    const question = questions[currentQuestionIndex];
                    questionText.textContent = question.text;
                    
                    // Update progress
                    progressText.textContent = `Pertanyaan ${currentQuestionIndex + 1} dari ${questions.length}`;
                    const progressPercentage = ((currentQuestionIndex + 1) / questions.length) * 100;
                    progressBar.style.width = `${progressPercentage}%`;

                    // Create answer buttons
                    answerButtonsContainer.innerHTML = '';
                    answers.forEach(answer => {
                        const button = document.createElement('button');
                        button.textContent = answer.text;
                        button.classList.add('w-full', 'bg-white', 'border', 'border-slate-300', 'text-slate-700', 'font-semibold', 'py-3', 'px-4', 'rounded-lg', 'hover:bg-sky-100', 'hover:border-sky-400', 'transition-colors', 'duration-200');
                        button.dataset.score = answer.score;
                        button.addEventListener('click', handleAnswer);
                        answerButtonsContainer.appendChild(button);
                    });

                    // Trigger animation
                    questionCard.classList.add('card-enter-active');
                }, 100); // Small delay for smoother transition
            }

            function handleAnswer(event) {
                const selectedScore = parseInt(event.target.dataset.score);
                userScore += selectedScore;
                currentQuestionIndex++;

                if (currentQuestionIndex < questions.length) {
                    displayQuestion();
                } else {
                    showResults();
                }
            }

            function showResults() {
                questionScreen.classList.add('hidden');
                resultScreen.classList.remove('hidden');

                // Update score display
                scoreValue.textContent = userScore;

                // Update gauge chart
                const maxScore = 36;
                const scorePercentage = (userScore / maxScore) * 100;
                const circumference = 100; // Since dasharray is 100, 100
                const offset = circumference - scorePercentage;
                gaugeFg.style.strokeDashoffset = offset;

                // Determine interpretation
                let level, interpretation;
                if (userScore <= 12) {
                    level = "Tingkat Stigma: Rendah";
                    interpretation = "Pandangan Anda menunjukkan tingkat empati yang tinggi dan pemahaman yang baik tentang isu kesehatan jiwa. Anda cenderung tidak setuju dengan stigma yang ada di masyarakat.";
                } else if (userScore <= 24) {
                    level = "Tingkat Stigma: Sedang";
                    interpretation = "Anda memiliki kesadaran tentang isu kesehatan jiwa, namun mungkin masih terpengaruh oleh beberapa pandangan umum di masyarakat. Ini adalah kesempatan baik untuk terus belajar dan memahami lebih dalam.";
                } else {
                    level = "Tingkat Stigma: Tinggi";
                    interpretation = "Hasil ini menunjukkan bahwa pandangan Anda mungkin sangat dipengaruhi oleh stigma yang umum di masyarakat. Kesadaran adalah langkah pertama, dan ada banyak sumber untuk belajar lebih banyak.";
                }

                stigmaLevel.textContent = level;
                stigmaInterpretation.textContent = interpretation;
            }

            function restartQuiz() {
                resultScreen.classList.add('hidden');
                welcomeScreen.classList.remove('hidden');
            }

            // --- EVENT LISTENERS ---
            startBtn.addEventListener('click', startQuiz);
            restartBtn.addEventListener('click', restartQuiz);
        });
    </script>
</body>
</html>
