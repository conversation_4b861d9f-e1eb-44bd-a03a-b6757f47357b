<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('tailadmin-index');
});

// Alternative routes for testing different templates
Route::get('/modern', function () {
    return view('index-modern');
});

Route::get('/old', function () {
    return view('index');
});

Route::get('/dashboard', function () {
    return view('tailadmin-student');
})->name('dashboard');

// Alternative routes for other templates
Route::get('/dashboard-modern', function () {
    return view('student-dashboard-modern');
})->name('dashboard.modern');

Route::get('/dashboard-old', function () {
    return view('dashboard');
})->name('dashboard.old');

// Role-specific dashboards
Route::get('/admin/dashboard', function () {
    return view('tailadmin-admin');
})->name('admin.dashboard');

Route::get('/guru/dashboard', function () {
    return view('guru-dashboard');
})->name('guru.dashboard');

// Alternative routes for other admin templates
Route::get('/admin/dashboard-modern', function () {
    return view('admin-dashboard-modern');
})->name('admin.dashboard.modern');

Route::get('/admin/dashboard-old', function () {
    return view('admin-dashboard');
})->name('admin.dashboard.old');

// Alternative routes for other teacher templates
Route::get('/guru/dashboard-modern', function () {
    return view('teacher-dashboard-modern');
})->name('guru.dashboard.modern');

Route::get('/guru/dashboard-old', function () {
    return view('guru-dashboard');
})->name('guru.dashboard.old');

Route::get('/orangtua/dashboard', function () {
    return view('tailadmin-parent');
})->name('orangtua.dashboard');

// Alternative routes for other parent templates
Route::get('/orangtua/dashboard-modern', function () {
    return view('parent-dashboard-modern');
})->name('orangtua.dashboard.modern');

Route::get('/orangtua/dashboard-old', function () {
    return view('orangtua-dashboard');
})->name('orangtua.dashboard.old');

// Assessment routes - TailAdmin versions
Route::get('/assessments', function () {
    return view('tailadmin-assessments');
})->name('assessments');

Route::get('/history', function () {
    return view('tailadmin-history');
})->name('history');

// Assessment form routes - TailAdmin version
Route::get('/assessment/{code}', function ($code) {
    return view('tailadmin-dynamic-form', compact('code'));
})->name('dynamic-form');

// Alternative route for old dynamic form
Route::get('/assessment-old/{code}', function ($code) {
    return view('dynamic-form', compact('code'));
})->name('dynamic-form.old');

Route::get('/srq20-form', function () {
    return redirect()->route('dynamic-form', ['code' => 'SRQ20']);
})->name('srq20-form');

// Assessment result route
Route::get('/assessment-result', function () {
    return view('tailadmin-result');
})->name('assessment-result');

// Alternative routes for other assessment templates
Route::get('/assessments-old', function () {
    return view('assessments');
})->name('assessments.old');

Route::get('/history-old', function () {
    return view('history');
})->name('history.old');
